# Hướng Dẫn Testing Admin Posts Management

## Tổng Quan

Tài liệu này cung cấp hướng dẫn chi tiết về việc testing các chức năng quản lý bài viết (posts) trong admin panel của NS Shop. Chúng ta sử dụng Playwright để thực hiện End-to-End (E2E) testing.

## Cấu Trúc Testing

### 1. Test Files

```
tests/e2e/admin/posts/
├── posts-list.spec.ts          # Tests cho danh sách bài viết
├── create-post.spec.ts         # Tests cho tạo bài viết mới
├── edit-post.spec.ts           # Tests cho chỉnh sửa bài viết
├── post-detail.spec.ts         # Tests cho xem chi tiết và preview
├── posts-stats.spec.ts         # Tests cho thống kê và dashboard
├── posts-api.spec.ts           # Tests cho API endpoints
└── posts-permissions.spec.ts   # Tests cho quyền truy cập và authentication
```

### 2. Helper Files

```
tests/e2e/helpers/
└── posts.helper.ts             # Helper functions và test data

tests/e2e/pages/admin/
├── posts-list.page.ts          # Page object cho danh sách posts
└── post-form.page.ts           # Page object cho form tạo/sửa posts
```

## Chức Năng Được Test

### 1. Posts List Management
- **Hiển thị danh sách**: Kiểm tra hiển thị posts, pagination, stats
- **Tìm kiếm**: Search theo title, content, excerpt
- **Lọc**: Filter theo status, featured, category, author
- **Sắp xếp**: Sort theo title, date, status
- **Bulk operations**: Select all, bulk delete, bulk publish
- **Individual actions**: Edit, delete, toggle featured, view

### 2. Create Post
- **Form validation**: Required fields, field formats
- **Auto-generation**: Slug từ title, excerpt từ content
- **Content editor**: Rich text editing, HTML content
- **Media management**: Featured image upload/selection
- **Categorization**: Category selection, tags management
- **Status management**: Draft, Published, Archived
- **SEO fields**: Meta title, meta description

### 3. Edit Post
- **Load existing data**: Populate form với data hiện tại
- **Update operations**: Cập nhật từng field riêng lẻ hoặc multiple fields
- **Status changes**: Thay đổi từ Draft → Published, etc.
- **Featured toggle**: Bật/tắt featured status
- **Validation**: Unique slug, required fields
- **Preview**: Xem trước changes

### 4. Post Detail & Preview
- **Preview functionality**: Xem post như end user
- **Draft preview**: Preview posts chưa publish
- **Rich content display**: HTML formatting, images, links
- **Metadata display**: Author, date, category, tags
- **SEO preview**: Meta tags, Open Graph

### 5. Stats & Dashboard
- **Statistics display**: Total, published, draft, featured counts
- **Real-time updates**: Stats update khi có changes
- **Quick actions**: Shortcuts đến common tasks
- **Recent activity**: Log các actions gần đây
- **Performance metrics**: Views, engagement (nếu có)

### 6. API Endpoints
- **CRUD operations**: GET, POST, PUT, DELETE
- **Bulk operations**: Bulk delete, bulk update
- **Filtering & search**: Query parameters
- **Pagination**: Page, limit, total
- **Error handling**: Validation errors, server errors
- **Authentication**: Token validation

### 7. Permissions & Authentication
- **Admin access**: Full CRUD permissions
- **Moderator access**: Limited permissions (no delete)
- **Authentication required**: Redirect to login
- **Session management**: Token expiration, refresh
- **Authorization checks**: Role-based access control

## Test Data

### Test Posts
```typescript
const TEST_POSTS = [
  {
    title: "Hướng dẫn chọn size áo thun phù hợp",
    content: "<p>Việc chọn size áo thun phù hợp...</p>",
    status: "PUBLISHED",
    featured: true,
    tags: ["hướng dẫn", "thời trang", "áo thun"]
  },
  // ... more test posts
];
```

### Test Categories
```typescript
const TEST_CATEGORIES = [
  {
    name: "Hướng dẫn",
    slug: "huong-dan",
    description: "Các bài viết hướng dẫn về thời trang"
  },
  // ... more categories
];
```

### Test Users
```typescript
const TEST_ADMIN_USERS = [
  {
    email: "<EMAIL>",
    password: "admin123",
    name: "Test Admin",
    role: "ADMIN"
  },
  {
    email: "<EMAIL>",
    password: "moderator123", 
    name: "Test Moderator",
    role: "MODERATOR"
  }
];
```

## Cách Chạy Tests

### 1. Chuẩn Bị Môi Trường

```bash
# Cài đặt dependencies
npm install

# Cài đặt Playwright browsers
npx playwright install

# Khởi động development server
npm run dev
```

### 2. Chạy Tests

```bash
# Chạy tất cả posts tests
npx playwright test tests/e2e/admin/posts/

# Chạy specific test file
npx playwright test tests/e2e/admin/posts/posts-list.spec.ts

# Chạy với UI mode
npx playwright test tests/e2e/admin/posts/ --ui

# Chạy với debug mode
npx playwright test tests/e2e/admin/posts/ --debug

# Chạy tests song song
npx playwright test tests/e2e/admin/posts/ --workers=4
```

### 3. Chạy Tests Theo Category

```bash
# Chỉ chạy list tests
npx playwright test tests/e2e/admin/posts/posts-list.spec.ts

# Chỉ chạy CRUD tests
npx playwright test tests/e2e/admin/posts/create-post.spec.ts tests/e2e/admin/posts/edit-post.spec.ts

# Chỉ chạy API tests
npx playwright test tests/e2e/admin/posts/posts-api.spec.ts

# Chỉ chạy permission tests
npx playwright test tests/e2e/admin/posts/posts-permissions.spec.ts
```

### 4. Xem Kết Quả

```bash
# Mở HTML report
npx playwright show-report

# Xem trace files
npx playwright show-trace trace.zip
```

## Test Patterns & Best Practices

### 1. Page Object Pattern
```typescript
// Sử dụng page objects cho reusability
const postsListPage = new PostsListPage(page);
await postsListPage.goto();
await postsListPage.searchPosts("hướng dẫn");
await postsListPage.expectPostsCount(2);
```

### 2. Database Helpers
```typescript
// Clean up và setup test data
await PostsDbHelper.cleanupTestData();
const testPosts = await PostsDbHelper.createTestPosts(adminId, categoryId);
```

### 3. API Testing
```typescript
// Test API endpoints directly
const response = await page.request.get("/api/admin/posts");
expect(response.status()).toBe(200);
const data = await response.json();
expect(data.success).toBe(true);
```

### 4. Authentication Testing
```typescript
// Sử dụng fixtures cho authenticated pages
test("should access admin posts", async ({ adminPage }) => {
  const postsPage = new PostsListPage(adminPage);
  await postsPage.goto();
  // Test với admin permissions
});
```

## Debugging Tests

### 1. Visual Debugging
```bash
# Chạy với headed mode
npx playwright test --headed

# Chạy với slow motion
npx playwright test --headed --slowMo=1000
```

### 2. Screenshots & Videos
```bash
# Chụp screenshot khi fail
npx playwright test --screenshot=only-on-failure

# Record video
npx playwright test --video=retain-on-failure
```

### 3. Debug Mode
```bash
# Pause execution để debug
npx playwright test --debug

# Hoặc thêm trong code
await page.pause();
```

## CI/CD Integration

### 1. GitHub Actions
```yaml
- name: Run Posts Tests
  run: npx playwright test tests/e2e/admin/posts/
  
- name: Upload Test Results
  uses: actions/upload-artifact@v3
  with:
    name: playwright-report
    path: playwright-report/
```

### 2. Test Reports
- HTML Report: Detailed test results với screenshots
- JUnit XML: Cho CI/CD integration
- JSON Report: Cho custom processing

## Troubleshooting

### Common Issues

1. **Test Timeout**: Tăng timeout trong playwright.config.ts
2. **Database Conflicts**: Đảm bảo cleanup data properly
3. **Authentication Issues**: Check token expiration và refresh
4. **Flaky Tests**: Add proper waits và assertions

### Performance Tips

1. **Parallel Execution**: Sử dụng workers để chạy song song
2. **Test Isolation**: Mỗi test độc lập với data riêng
3. **Selective Testing**: Chỉ chạy tests cần thiết
4. **Database Optimization**: Optimize test data creation

## Kết Luận

Test suite này cung cấp coverage toàn diện cho posts management functionality, bao gồm:

- ✅ UI interactions và user flows
- ✅ API endpoints và data validation  
- ✅ Authentication và authorization
- ✅ Error handling và edge cases
- ✅ Performance và scalability
- ✅ Cross-browser compatibility

Việc maintain và update tests thường xuyên sẽ đảm bảo quality và reliability của posts management system.
