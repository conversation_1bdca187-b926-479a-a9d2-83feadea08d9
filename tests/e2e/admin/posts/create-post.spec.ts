import { test, expect } from "../../test-setup";
import { PostFormPage } from "../../pages/admin/post-form.page";
import { PostsListPage } from "../../pages/admin/posts-list.page";
import { PostsDbHelper, TEST_CATEGORIES, TEST_ADMIN_USERS } from "../../helpers/posts.helper";

test.describe("Create Post Page", () => {
  let postFormPage: PostFormPage;
  let postsListPage: PostsListPage;
  let testAdmin: any;
  let testCategories: any[];

  test.beforeEach(async ({ adminPage }) => {
    postFormPage = new PostFormPage(adminPage);
    postsListPage = new PostsListPage(adminPage);
    
    // Clean up test data
    await PostsDbHelper.cleanupTestData();
    
    // Create test admin user
    testAdmin = await PostsDbHelper.createTestAdminUser(TEST_ADMIN_USERS[0]);
    
    // Create test categories
    testCategories = await PostsDbHelper.createTestCategories();
    
    // Navigate to create post page
    await postFormPage.gotoCreate();
    await postFormPage.waitForPageLoad();
  });

  test.afterAll(async () => {
    await PostsDbHelper.cleanupTestData();
  });

  test("should display create post page correctly", async () => {
    // Check page title and elements
    await postFormPage.expectCreatePageTitle();
    await expect(postFormPage.backButton).toBeVisible();
    
    // Check form elements are present
    await expect(postFormPage.titleInput).toBeVisible();
    await expect(postFormPage.contentEditor).toBeVisible();
    await expect(postFormPage.statusSelect).toBeVisible();
    await expect(postFormPage.saveButton).toBeVisible();
    
    // Check default values
    await expect(postFormPage.statusSelect).toContainText("DRAFT");
    expect(await postFormPage.isFeatured()).toBe(false);
  });

  test("should validate required fields", async () => {
    // Try to submit empty form
    await postFormPage.save();
    
    // Should show validation errors
    await postFormPage.expectTitleError("Tiêu đề là bắt buộc");
    await postFormPage.expectContentError("Nội dung là bắt buộc");
    
    // Fill only title
    await postFormPage.fillTitle("Test Post");
    await postFormPage.save();
    
    // Should still show content error
    await postFormPage.expectContentError("Nội dung là bắt buộc");
    
    // Fill content
    await postFormPage.fillContent("Test content");
    await postFormPage.save();
    
    // Should not show errors anymore
    await postFormPage.expectNoErrors();
  });

  test("should auto-generate slug from title", async () => {
    // Fill title
    await postFormPage.fillTitle("Hướng Dẫn Chọn Size Áo Thun");
    
    // Wait for slug generation
    await postFormPage.page.waitForTimeout(1000);
    
    // Check slug is generated
    const slugValue = await postFormPage.getSlugValue();
    expect(slugValue).toBe("huong-dan-chon-size-ao-thun");
  });

  test("should allow manual slug editing", async () => {
    // Fill title and auto-generated slug
    await postFormPage.fillTitle("Test Post Title");
    await postFormPage.page.waitForTimeout(500);
    
    // Manually edit slug
    await postFormPage.fillSlug("custom-slug-test");
    
    // Check slug value
    const slugValue = await postFormPage.getSlugValue();
    expect(slugValue).toBe("custom-slug-test");
  });

  test("should create post as draft", async () => {
    const postData = {
      title: "Test Draft Post",
      content: "This is a test draft post content",
      excerpt: "Test excerpt",
      status: "DRAFT" as const,
      featured: false,
      tags: ["test", "draft"],
    };
    
    // Fill form
    await postFormPage.fillPostForm(postData);
    
    // Save as draft
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Tạo bài viết thành công");
    
    // Should redirect to posts list
    await postFormPage.expectRedirectToPostsList();
    
    // Verify post exists in database
    const createdPost = await PostsDbHelper.getPostBySlug("test-draft-post");
    expect(createdPost).toBeTruthy();
    expect(createdPost?.status).toBe("DRAFT");
    expect(createdPost?.featured).toBe(false);
  });

  test("should create and publish post", async () => {
    const postData = {
      title: "Test Published Post",
      content: "This is a test published post content",
      excerpt: "Test published excerpt",
      status: "PUBLISHED" as const,
      featured: true,
      category: testCategories[0].name,
      tags: ["test", "published"],
    };
    
    // Fill form
    await postFormPage.fillPostForm(postData);
    
    // Publish post
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Tạo bài viết thành công");
    
    // Verify post exists and is published
    const createdPost = await PostsDbHelper.getPostBySlug("test-published-post");
    expect(createdPost).toBeTruthy();
    expect(createdPost?.status).toBe("PUBLISHED");
    expect(createdPost?.featured).toBe(true);
    expect(createdPost?.categoryId).toBe(testCategories[0].id);
  });

  test("should handle tags correctly", async () => {
    // Fill basic info
    await postFormPage.fillTitle("Test Post with Tags");
    await postFormPage.fillContent("Test content");
    
    // Add tags
    await postFormPage.addTag("thời trang");
    await postFormPage.addTag("hướng dẫn");
    await postFormPage.addTag("mẹo hay");
    
    // Check tags are displayed
    const tags = await postFormPage.getSelectedTags();
    expect(tags).toContain("thời trang");
    expect(tags).toContain("hướng dẫn");
    expect(tags).toContain("mẹo hay");
    
    // Remove a tag
    await postFormPage.removeTag("hướng dẫn");
    
    // Check tag is removed
    const updatedTags = await postFormPage.getSelectedTags();
    expect(updatedTags).not.toContain("hướng dẫn");
    expect(updatedTags).toContain("thời trang");
    expect(updatedTags).toContain("mẹo hay");
    
    // Save post
    await postFormPage.save();
    
    // Verify tags in database
    const createdPost = await PostsDbHelper.getPostBySlug("test-post-with-tags");
    expect(createdPost?.tags).toEqual(expect.arrayContaining(["thời trang", "mẹo hay"]));
    expect(createdPost?.tags).not.toContain("hướng dẫn");
  });

  test("should handle category selection", async () => {
    // Fill basic info
    await postFormPage.fillTitle("Test Post with Category");
    await postFormPage.fillContent("Test content");
    
    // Select category
    await postFormPage.selectCategory(testCategories[1].name);
    
    // Save post
    await postFormPage.save();
    
    // Verify category in database
    const createdPost = await PostsDbHelper.getPostBySlug("test-post-with-category");
    expect(createdPost?.categoryId).toBe(testCategories[1].id);
  });

  test("should handle featured image", async () => {
    // Fill basic info
    await postFormPage.fillTitle("Test Post with Image");
    await postFormPage.fillContent("Test content");
    
    // Set featured image
    const imageUrl = "https://example.com/test-image.jpg";
    await postFormPage.setFeaturedImage(imageUrl);
    
    // Check image is displayed
    expect(await postFormPage.hasFeaturedImage()).toBe(true);
    
    // Save post
    await postFormPage.save();
    
    // Verify image in database
    const createdPost = await PostsDbHelper.getPostBySlug("test-post-with-image");
    expect(createdPost?.featuredImage).toBe(imageUrl);
  });

  test("should handle rich text content", async () => {
    // Fill basic info
    await postFormPage.fillTitle("Test Post with Rich Content");
    
    // Add rich content
    const richContent = `
      <h2>Heading 2</h2>
      <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
      <ul>
        <li>List item 1</li>
        <li>List item 2</li>
      </ul>
    `;
    
    await postFormPage.fillContentHTML(richContent);
    
    // Save post
    await postFormPage.save();
    
    // Verify content in database
    const createdPost = await PostsDbHelper.getPostBySlug("test-post-with-rich-content");
    expect(createdPost?.content).toContain("<h2>");
    expect(createdPost?.content).toContain("<strong>");
    expect(createdPost?.content).toContain("<ul>");
  });

  test("should handle SEO meta fields", async () => {
    // Fill basic info
    await postFormPage.fillTitle("Test Post with SEO");
    await postFormPage.fillContent("Test content");
    
    // Fill SEO fields
    await postFormPage.fillMetaTitle("Custom Meta Title");
    await postFormPage.fillMetaDescription("Custom meta description for SEO");
    
    // Save post
    await postFormPage.save();
    
    // Note: This test assumes SEO fields are implemented in the form
    // Adjust based on actual implementation
  });

  test("should validate slug uniqueness", async () => {
    // Create first post
    await postFormPage.fillTitle("First Post");
    await postFormPage.fillContent("First post content");
    await postFormPage.fillSlug("unique-slug");
    await postFormPage.save();
    
    // Navigate back to create page
    await postFormPage.gotoCreate();
    
    // Try to create second post with same slug
    await postFormPage.fillTitle("Second Post");
    await postFormPage.fillContent("Second post content");
    await postFormPage.fillSlug("unique-slug");
    await postFormPage.save();
    
    // Should show slug error
    await postFormPage.expectSlugError("Slug đã tồn tại");
  });

  test("should handle form cancellation", async () => {
    // Fill some data
    await postFormPage.fillTitle("Test Post to Cancel");
    await postFormPage.fillContent("Some content");
    
    // Cancel form
    await postFormPage.cancel();
    
    // Should redirect to posts list
    await postFormPage.expectRedirectToPostsList();
    
    // Post should not be created
    const post = await PostsDbHelper.getPostBySlug("test-post-to-cancel");
    expect(post).toBeNull();
  });

  test("should handle back navigation", async () => {
    // Click back button
    await postFormPage.goBack();
    
    // Should redirect to posts list
    await postFormPage.expectRedirectToPostsList();
  });

  test("should show loading state during save", async () => {
    // Intercept API call to add delay
    await postFormPage.page.route("/api/admin/posts", async (route) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    // Fill and submit form
    await postFormPage.fillTitle("Test Loading State");
    await postFormPage.fillContent("Test content");
    await postFormPage.save();
    
    // Should show saving state
    await postFormPage.expectSavingState();
    
    // Form should be disabled during save
    await postFormPage.expectFormDisabled();
  });

  test("should handle save errors", async () => {
    // Intercept API call to return error
    await postFormPage.page.route("/api/admin/posts", async (route) => {
      await route.fulfill({
        status: 400,
        contentType: "application/json",
        body: JSON.stringify({ 
          success: false, 
          error: "Có lỗi xảy ra khi tạo bài viết" 
        }),
      });
    });
    
    // Fill and submit form
    await postFormPage.fillTitle("Test Error Handling");
    await postFormPage.fillContent("Test content");
    await postFormPage.save();
    
    // Should show error message
    await postFormPage.expectErrorMessage("Có lỗi xảy ra khi tạo bài viết");
    
    // Form should remain enabled
    await postFormPage.expectFormEnabled();
  });

  test("should auto-save draft periodically", async () => {
    // This test would require auto-save functionality to be implemented
    // Skip for now or implement based on actual feature
    test.skip();
  });

  test("should warn about unsaved changes", async () => {
    // Fill some data
    await postFormPage.fillTitle("Unsaved Changes Test");
    await postFormPage.fillContent("Some content");
    
    // Try to navigate away
    await postFormPage.goBack();
    
    // Should show unsaved changes dialog (if implemented)
    // This test depends on actual implementation
    test.skip();
  });

  test("should handle excerpt auto-generation", async () => {
    // Fill title and content
    await postFormPage.fillTitle("Test Auto Excerpt");
    const longContent = "This is a very long content that should be automatically truncated to create an excerpt. ".repeat(10);
    await postFormPage.fillContent(longContent);
    
    // Save without filling excerpt
    await postFormPage.save();
    
    // Verify excerpt is auto-generated
    const createdPost = await PostsDbHelper.getPostBySlug("test-auto-excerpt");
    expect(createdPost?.excerpt).toBeTruthy();
    expect(createdPost?.excerpt?.length).toBeLessThanOrEqual(200);
  });

  test("should preserve manual excerpt over auto-generation", async () => {
    // Fill all fields including manual excerpt
    await postFormPage.fillTitle("Test Manual Excerpt");
    await postFormPage.fillContent("Long content that would generate auto excerpt");
    await postFormPage.fillExcerpt("This is a custom excerpt");
    
    // Save post
    await postFormPage.save();
    
    // Verify manual excerpt is preserved
    const createdPost = await PostsDbHelper.getPostBySlug("test-manual-excerpt");
    expect(createdPost?.excerpt).toBe("This is a custom excerpt");
  });
});
