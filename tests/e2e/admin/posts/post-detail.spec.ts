import { test, expect } from "../../test-setup";
import { PostsListPage } from "../../pages/admin/posts-list.page";
import { PostFormPage } from "../../pages/admin/post-form.page";
import { PostsDbHelper, TEST_POSTS, TEST_CATEGORIES, TEST_ADMIN_USERS } from "../../helpers/posts.helper";

test.describe("Post Detail and Preview", () => {
  let postsListPage: PostsListPage;
  let postFormPage: PostFormPage;
  let testAdmin: any;
  let testCategories: any[];
  let testPosts: any[];

  test.beforeEach(async ({ adminPage }) => {
    postsListPage = new PostsListPage(adminPage);
    postFormPage = new PostFormPage(adminPage);
    
    // Clean up test data
    await PostsDbHelper.cleanupTestData();
    
    // Create test admin user
    testAdmin = await PostsDbHelper.createTestAdminUser(TEST_ADMIN_USERS[0]);
    
    // Create test categories
    testCategories = await PostsDbHelper.createTestCategories();
    
    // Create test posts
    testPosts = await PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id);
  });

  test.afterAll(async () => {
    await PostsDbHelper.cleanupTestData();
  });

  test("should view post from posts list", async () => {
    // Navigate to posts list
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    const postToView = testPosts[0];
    
    // Click view button on first post
    await postsListPage.viewPost(postToView.title);
    
    // Should navigate to post detail/view page
    // This depends on implementation - might be admin detail page or public post page
    await expect(postsListPage.page).toHaveURL(new RegExp(`/posts/${postToView.slug}|/admin/posts/${postToView.id}`));
  });

  test("should preview post from edit page", async () => {
    const postToPreview = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToPreview.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should open preview in new tab
    await newPage.waitForLoadState();
    
    // Check preview page content
    await expect(newPage.locator(`h1:has-text("${postToPreview.title}")`)).toBeVisible();
    await expect(newPage.locator("text=Việc chọn size")).toBeVisible(); // Part of test content
    
    // Close preview tab
    await newPage.close();
  });

  test("should preview draft post", async () => {
    const draftPost = testPosts.find(p => p.status === "DRAFT");
    if (!draftPost) {
      test.skip("No draft post available for testing");
      return;
    }
    
    // Navigate to edit page
    await postFormPage.gotoEdit(draftPost.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should open preview even for draft
    await newPage.waitForLoadState();
    await expect(newPage.locator(`h1:has-text("${draftPost.title}")`)).toBeVisible();
    
    // Should show draft indicator
    await expect(newPage.locator("text=Bản nháp")).toBeVisible();
    
    await newPage.close();
  });

  test("should preview post with featured image", async () => {
    // Create a post with featured image
    const postWithImage = testPosts[0];
    
    // Update post to have featured image
    await postFormPage.page.evaluate(async (postId) => {
      await fetch(`/api/admin/posts/${postId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ featuredImage: 'https://example.com/test-image.jpg' })
      });
    }, postWithImage.id);
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postWithImage.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should show featured image in preview
    await newPage.waitForLoadState();
    await expect(newPage.locator('img[src*="test-image.jpg"]')).toBeVisible();
    
    await newPage.close();
  });

  test("should preview post with tags", async () => {
    const postWithTags = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postWithTags.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should show tags in preview
    await newPage.waitForLoadState();
    
    // Check if tags are displayed
    for (const tag of postWithTags.tags) {
      await expect(newPage.locator(`text=${tag}`)).toBeVisible();
    }
    
    await newPage.close();
  });

  test("should preview post with category", async () => {
    const postWithCategory = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postWithCategory.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should show category in preview
    await newPage.waitForLoadState();
    await expect(newPage.locator(`text=${testCategories[0].name}`)).toBeVisible();
    
    await newPage.close();
  });

  test("should show post metadata in preview", async () => {
    const postToPreview = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToPreview.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should show post metadata
    await newPage.waitForLoadState();
    
    // Check author name
    await expect(newPage.locator(`text=${testAdmin.name}`)).toBeVisible();
    
    // Check publish date (for published posts)
    if (postToPreview.status === "PUBLISHED") {
      await expect(newPage.locator("time")).toBeVisible();
    }
    
    await newPage.close();
  });

  test("should handle preview of unsaved changes", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Make changes without saving
    const newTitle = "Unsaved Preview Title";
    await postFormPage.fillTitle(newTitle);
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Preview should show either:
    // 1. Original content (if preview doesn't include unsaved changes)
    // 2. Updated content (if preview includes unsaved changes)
    // This depends on implementation
    await newPage.waitForLoadState();
    
    // Check if preview shows unsaved changes or original content
    const hasUnsavedChanges = await newPage.locator(`h1:has-text("${newTitle}")`).isVisible();
    const hasOriginalContent = await newPage.locator(`h1:has-text("${postToEdit.title}")`).isVisible();
    
    expect(hasUnsavedChanges || hasOriginalContent).toBe(true);
    
    await newPage.close();
  });

  test("should navigate between posts in detail view", async () => {
    // This test assumes there's a post detail page with navigation
    // Skip if not implemented
    test.skip("Post detail navigation not implemented");
  });

  test("should show post statistics in detail view", async () => {
    // This test assumes post detail shows view counts, comments, etc.
    // Skip if not implemented
    test.skip("Post statistics not implemented");
  });

  test("should handle preview errors gracefully", async () => {
    const postToPreview = testPosts[0];
    
    // Intercept preview request to return error
    await postFormPage.page.route(`**/posts/${postToPreview.slug}*`, async (route) => {
      await route.fulfill({
        status: 404,
        contentType: "text/html",
        body: "<html><body><h1>404 - Post Not Found</h1></body></html>",
      });
    });
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToPreview.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should show error page
    await newPage.waitForLoadState();
    await expect(newPage.locator("text=404")).toBeVisible();
    
    await newPage.close();
  });

  test("should preview post with rich content formatting", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Add rich content
    const richContent = `
      <h2>Heading 2</h2>
      <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
      <ul>
        <li>List item 1</li>
        <li>List item 2</li>
      </ul>
      <blockquote>This is a quote</blockquote>
    `;
    
    await postFormPage.fillContentHTML(richContent);
    
    // Save changes first
    await postFormPage.save();
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should show formatted content
    await newPage.waitForLoadState();
    await expect(newPage.locator("h2")).toBeVisible();
    await expect(newPage.locator("strong")).toBeVisible();
    await expect(newPage.locator("em")).toBeVisible();
    await expect(newPage.locator("ul li")).toBeVisible();
    await expect(newPage.locator("blockquote")).toBeVisible();
    
    await newPage.close();
  });

  test("should show correct SEO meta tags in preview", async () => {
    const postToPreview = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToPreview.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Check meta tags
    await newPage.waitForLoadState();
    
    // Check title tag
    await expect(newPage).toHaveTitle(new RegExp(postToPreview.title));
    
    // Check meta description (if excerpt exists)
    if (postToPreview.excerpt) {
      const metaDescription = await newPage.locator('meta[name="description"]').getAttribute('content');
      expect(metaDescription).toContain(postToPreview.excerpt);
    }
    
    // Check Open Graph tags
    const ogTitle = await newPage.locator('meta[property="og:title"]').getAttribute('content');
    expect(ogTitle).toBe(postToPreview.title);
    
    await newPage.close();
  });

  test("should handle preview authentication for draft posts", async () => {
    const draftPost = testPosts.find(p => p.status === "DRAFT");
    if (!draftPost) {
      test.skip("No draft post available for testing");
      return;
    }
    
    // Navigate to edit page
    await postFormPage.gotoEdit(draftPost.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Preview should work for authenticated admin
    await newPage.waitForLoadState();
    await expect(newPage.locator(`h1:has-text("${draftPost.title}")`)).toBeVisible();
    
    await newPage.close();
  });

  test("should show reading time in preview", async () => {
    const postToPreview = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToPreview.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should show reading time (if implemented)
    await newPage.waitForLoadState();
    
    // Look for reading time indicator
    const readingTime = newPage.locator("text=/\\d+ phút đọc/");
    if (await readingTime.isVisible()) {
      await expect(readingTime).toBeVisible();
    }
    
    await newPage.close();
  });

  test("should handle preview with special characters in content", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Add content with special characters
    const specialContent = `
      <p>Content with special characters: áàảãạ êếềểễệ ôốồổỗộ</p>
      <p>Symbols: @#$%^&*()_+-=[]{}|;':",./<>?</p>
      <p>Unicode: 🎉 🚀 ⭐ 💡</p>
    `;
    
    await postFormPage.fillContentHTML(specialContent);
    await postFormPage.save();
    
    // Click preview button
    const [newPage] = await Promise.all([
      postFormPage.page.context().waitForEvent('page'),
      postFormPage.preview()
    ]);
    
    // Should display special characters correctly
    await newPage.waitForLoadState();
    await expect(newPage.locator("text=áàảãạ")).toBeVisible();
    await expect(newPage.locator("text=🎉")).toBeVisible();
    
    await newPage.close();
  });
});
