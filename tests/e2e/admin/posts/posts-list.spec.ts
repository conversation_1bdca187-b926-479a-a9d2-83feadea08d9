import { test, expect } from "../../test-setup";
import { PostsListPage } from "../../pages/admin/posts-list.page";
import {
  PostsDbHelper,
  TEST_POSTS,
  TEST_CATEGORIES,
  TEST_ADMIN_USERS,
} from "../../helpers/posts.helper";

test.describe("Posts List Page", () => {
  let postsListPage: PostsListPage;
  let testAdmin: any;
  let testCategories: any[];
  let testPosts: any[];

  test.beforeEach(async ({ adminPage }) => {
    postsListPage = new PostsListPage(adminPage);

    // Clean up test data
    await PostsDbHelper.cleanupTestData();

    // Create test admin user
    testAdmin = await PostsDbHelper.createTestAdminUser(TEST_ADMIN_USERS[0]);

    // Create test categories
    testCategories = await PostsDbHelper.createTestCategories();

    // Create test posts
    testPosts = await PostsDbHelper.createTestPosts(
      testAdmin.id,
      testCategories[0].id
    );

    // Navigate to posts list page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
  });

  test.afterAll(async () => {
    await PostsDbHelper.cleanupTestData();
  });

  test("should display posts list page correctly", async () => {
    // Check page title and description
    await expect(postsListPage.pageTitle).toBeVisible();
    await expect(postsListPage.pageDescription).toBeVisible();
    await expect(postsListPage.createPostButton).toBeVisible();

    // Check that posts are displayed
    await postsListPage.expectPostsCount(TEST_POSTS.length);

    // Check post titles are visible
    for (const post of TEST_POSTS) {
      await postsListPage.expectPostVisible(post.title);
    }
  });

  test("should display posts statistics correctly", async () => {
    // Check stats are visible
    await expect(postsListPage.totalPostsCount).toBeVisible();
    await expect(postsListPage.publishedPostsCount).toBeVisible();
    await expect(postsListPage.draftPostsCount).toBeVisible();
    await expect(postsListPage.featuredPostsCount).toBeVisible();

    // Verify counts match test data
    const publishedCount = TEST_POSTS.filter(
      (p) => p.status === "PUBLISHED"
    ).length;
    const draftCount = TEST_POSTS.filter((p) => p.status === "DRAFT").length;
    const featuredCount = TEST_POSTS.filter((p) => p.featured).length;

    await expect(postsListPage.totalPostsCount).toContainText(
      TEST_POSTS.length.toString()
    );
    await expect(postsListPage.publishedPostsCount).toContainText(
      publishedCount.toString()
    );
    await expect(postsListPage.draftPostsCount).toContainText(
      draftCount.toString()
    );
    await expect(postsListPage.featuredPostsCount).toContainText(
      featuredCount.toString()
    );
  });

  test("should search posts correctly", async () => {
    // Search for specific post
    await postsListPage.search("Hướng dẫn");

    // Should show only matching posts
    await postsListPage.expectPostVisible(
      "Hướng dẫn chọn size áo thun phù hợp"
    );
    await postsListPage.expectPostNotVisible("Xu hướng thời trang mùa hè 2024");

    // Clear search
    await postsListPage.clearSearch();

    // Should show all posts again
    await postsListPage.expectPostsCount(TEST_POSTS.length);
  });

  test("should filter posts by status", async () => {
    // Filter by PUBLISHED status
    await postsListPage.filterByStatus("PUBLISHED");

    // Should show only published posts
    const publishedPosts = TEST_POSTS.filter((p) => p.status === "PUBLISHED");
    await postsListPage.expectPostsCount(publishedPosts.length);

    for (const post of publishedPosts) {
      await postsListPage.expectPostVisible(post.title);
    }

    // Filter by DRAFT status
    await postsListPage.filterByStatus("DRAFT");

    // Should show only draft posts
    const draftPosts = TEST_POSTS.filter((p) => p.status === "DRAFT");
    await postsListPage.expectPostsCount(draftPosts.length);

    // Clear filters
    await postsListPage.clearAllFilters();
    await postsListPage.expectPostsCount(TEST_POSTS.length);
  });

  test("should filter posts by featured status", async () => {
    // Filter by featured posts
    await postsListPage.filterByFeatured(true);

    // Should show only featured posts
    const featuredPosts = TEST_POSTS.filter((p) => p.featured);
    await postsListPage.expectPostsCount(featuredPosts.length);

    // Clear featured filter
    await postsListPage.filterByFeatured(false);
    await postsListPage.expectPostsCount(TEST_POSTS.length);
  });

  test("should filter posts by category", async () => {
    // Filter by category
    await postsListPage.filterByCategory(testCategories[0].name);

    // Should show posts from that category
    await postsListPage.expectPostsCount(TEST_POSTS.length); // All test posts use the same category

    // Clear filters
    await postsListPage.clearAllFilters();
    await postsListPage.expectPostsCount(TEST_POSTS.length);
  });

  test("should sort posts correctly", async () => {
    // Sort by title ascending
    await postsListPage.sortByTitle("asc");

    const titlesAsc = await postsListPage.getPostTitles();
    expect(titlesAsc).toEqual([...titlesAsc].sort());

    // Sort by title descending
    await postsListPage.sortByTitle("desc");

    const titlesDesc = await postsListPage.getPostTitles();
    expect(titlesDesc).toEqual([...titlesDesc].sort().reverse());

    // Sort by date (newest first)
    await postsListPage.sortByDate("desc");

    // Sort by status
    await postsListPage.sortByStatus("asc");

    const statuses = await postsListPage.getPostStatuses();
    expect(statuses).toEqual([...statuses].sort());
  });

  test("should handle post selection correctly", async () => {
    // Select individual posts
    await postsListPage.selectPostByIndex(0);
    await postsListPage.selectPostByIndex(1);

    // Bulk actions should be visible
    await postsListPage.expectBulkActionsVisible();
    await postsListPage.expectSelectedCount(2);

    // Select all posts
    await postsListPage.selectAllPosts();
    await postsListPage.expectSelectedCount(TEST_POSTS.length);

    // Deselect all
    await postsListPage.deselectAllPosts();
    await postsListPage.expectBulkActionsHidden();
  });

  test("should perform bulk delete operation", async () => {
    // Select multiple posts
    await postsListPage.selectPostByIndex(0);
    await postsListPage.selectPostByIndex(1);

    // Perform bulk delete
    await postsListPage.bulkDelete();

    // Should show success message
    await postsListPage.expectSuccessToast("Đã xóa 2 bài viết thành công");

    // Should have fewer posts
    await postsListPage.expectPostsCount(TEST_POSTS.length - 2);
  });

  test("should perform bulk publish operation", async () => {
    // Select draft posts
    const draftPost = TEST_POSTS.find((p) => p.status === "DRAFT");
    if (draftPost) {
      await postsListPage.selectPostByTitle(draftPost.title);

      // Perform bulk publish
      await postsListPage.bulkPublish();

      // Should show success message
      await postsListPage.expectSuccessToast(
        "Đã xuất bản 1 bài viết thành công"
      );
    }
  });

  test("should delete individual post", async () => {
    const postToDelete = TEST_POSTS[0];

    // Delete post
    await postsListPage.deletePost(postToDelete.title);

    // Should show success message
    await postsListPage.expectSuccessToast("Xóa bài viết thành công");

    // Post should not be visible
    await postsListPage.expectPostNotVisible(postToDelete.title);
    await postsListPage.expectPostsCount(TEST_POSTS.length - 1);
  });

  test("should toggle featured status", async () => {
    const postToToggle = TEST_POSTS.find((p) => !p.featured);
    if (postToToggle) {
      // Toggle featured status
      await postsListPage.toggleFeatured(postToToggle.title);

      // Should show success message
      await postsListPage.expectSuccessToast(
        "Cập nhật trạng thái nổi bật thành công"
      );
    }
  });

  test("should navigate to edit post", async () => {
    const postToEdit = TEST_POSTS[0];

    // Click edit button
    await postsListPage.editPost(postToEdit.title);

    // Should navigate to edit page
    await expect(postsListPage.page).toHaveURL(
      new RegExp(`/admin/posts/.+/edit`)
    );
  });

  test("should navigate to create post", async () => {
    // Click create post button
    await postsListPage.clickCreatePost();

    // Should navigate to create page
    await expect(postsListPage.page).toHaveURL("/admin/posts/create");
  });

  test("should handle pagination correctly", async () => {
    // Create more posts to test pagination
    const morePosts = Array.from({ length: 25 }, (_, i) => ({
      title: `Test Post ${i + 1}`,
      content: `Content for test post ${i + 1}`,
      status: "PUBLISHED" as const,
      featured: false,
      tags: [],
      authorId: testAdmin.id,
      categoryId: testCategories[0].id,
    }));

    // Create additional posts in database
    for (const post of morePosts) {
      await PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id);
    }

    // Reload page
    await postsListPage.goto();

    // Should show pagination
    await expect(postsListPage.pagination).toBeVisible();

    // Go to next page
    await postsListPage.goToNextPage();
    await expect(postsListPage.getCurrentPageNumber()).resolves.toBe(2);

    // Go back to first page
    await postsListPage.goToPrevPage();
    await expect(postsListPage.getCurrentPageNumber()).resolves.toBe(1);

    // Change page size
    await postsListPage.changePageSize(10);
    await postsListPage.expectPostsCount(10);
  });

  test("should show empty state when no posts", async () => {
    // Delete all posts including test data
    await PostsDbHelper.cleanupTestData();

    // Reload page to see empty state
    await postsListPage.page.reload();
    await postsListPage.waitForPageLoad();

    // Should show empty state
    await postsListPage.expectEmptyState();
    await expect(
      postsListPage.page.locator("text=Chưa có bài viết nào")
    ).toBeVisible();
  });

  test("should handle loading states", async () => {
    // Intercept API call to add delay
    await postsListPage.page.route("/api/admin/posts*", async (route) => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      await route.continue();
    });

    // Navigate to page
    await postsListPage.goto();

    // Should show loading state
    await postsListPage.expectLoadingState();

    // Wait for loading to complete
    await expect(postsListPage.loadingSpinner).not.toBeVisible();
    await postsListPage.expectPostsCount(TEST_POSTS.length);
  });

  test("should handle error states", async () => {
    // Intercept API call to return error
    await postsListPage.page.route("/api/admin/posts*", async (route) => {
      await route.fulfill({
        status: 500,
        contentType: "application/json",
        body: JSON.stringify({ error: "Internal server error" }),
      });
    });

    // Navigate to page
    await postsListPage.goto();

    // Should show error state
    await postsListPage.expectErrorState();
    await expect(
      postsListPage.page.locator("text=Có lỗi xảy ra")
    ).toBeVisible();

    // Should have retry button
    await expect(postsListPage.retryButton).toBeVisible();
  });

  test("should combine multiple filters", async () => {
    // Apply multiple filters
    await postsListPage.search("thời trang");
    await postsListPage.filterByStatus("PUBLISHED");
    await postsListPage.filterByFeatured(false);

    // Should show posts matching all criteria
    const matchingPosts = TEST_POSTS.filter(
      (p) =>
        p.title.toLowerCase().includes("thời trang") &&
        p.status === "PUBLISHED" &&
        !p.featured
    );

    await postsListPage.expectPostsCount(matchingPosts.length);

    // Clear all filters
    await postsListPage.clearAllFilters();
    await postsListPage.expectPostsCount(TEST_POSTS.length);
  });

  test("should maintain filters across page refreshes", async () => {
    // Apply filters
    await postsListPage.search("Hướng dẫn");
    await postsListPage.filterByStatus("PUBLISHED");

    // Refresh page
    await postsListPage.page.reload();
    await postsListPage.waitForPageLoad();

    // Filters should be maintained (if implemented)
    // This test might need adjustment based on actual implementation
  });
});
