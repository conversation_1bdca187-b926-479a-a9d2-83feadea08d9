import { test, expect } from "../../test-setup";
import { PostsListPage } from "../../pages/admin/posts-list.page";
import {
  PostsDbHelper,
  TEST_POSTS,
  TEST_ADMIN_USERS,
} from "../../helpers/posts.helper";

test.describe("Posts List Page - Simplified", () => {
  let postsListPage: PostsListPage;
  let testAdmin: any;
  let testCategories: any[];
  let testPosts: any[];

  test.beforeEach(async ({ adminPage }) => {
    postsListPage = new PostsListPage(adminPage);

    // Clean up test data
    await PostsDbHelper.cleanupTestData();

    // Create test admin user
    testAdmin = await PostsDbHelper.createTestAdminUser(TEST_ADMIN_USERS[0]);

    // Create test categories
    testCategories = await PostsDbHelper.createTestCategories();

    // Create test posts
    testPosts = await PostsDbHelper.createTestPosts(
      testAdmin.id,
      testCategories[0].id
    );

    // Navigate to posts page
    await postsListPage.goto();
  });

  test.afterEach(async () => {
    // Clean up after each test
    await PostsDbHelper.cleanupTestData();
  });

  // Core functionality tests that match actual UI
  test("should display posts list page correctly", async () => {
    // Should show page title (specific h1 in main content)
    await expect(postsListPage.page.locator("main h1")).toContainText(
      "Quản lý bài viết"
    );

    // Should show posts table
    await expect(postsListPage.postsTable).toBeVisible();

    // Should show at least some posts
    await expect(postsListPage.postRows).toHaveCount(testPosts.length);

    // Should show create button
    await expect(postsListPage.createPostButton).toBeVisible();
  });

  test("should display posts statistics correctly", async () => {
    // Count posts by status from test data
    const publishedCount = TEST_POSTS.filter(
      (p) => p.status === "PUBLISHED"
    ).length;
    const draftCount = TEST_POSTS.filter((p) => p.status === "DRAFT").length;
    const archivedCount = TEST_POSTS.filter(
      (p) => p.status === "ARCHIVED"
    ).length;
    const featuredCount = TEST_POSTS.filter((p) => p.featured).length;

    // Check stats display
    await expect(postsListPage.totalPostsCount).toContainText(
      TEST_POSTS.length.toString()
    );
    await expect(postsListPage.publishedPostsCount).toContainText(
      publishedCount.toString()
    );
    await expect(postsListPage.draftPostsCount).toContainText(
      draftCount.toString()
    );
    await expect(postsListPage.featuredPostsCount).toContainText(
      featuredCount.toString()
    );
  });

  test("should have working search input", async () => {
    // Should show search input
    await expect(postsListPage.searchInput).toBeVisible();

    // Should be able to type in search
    await postsListPage.searchInput.fill("test search");
    await expect(postsListPage.searchInput).toHaveValue("test search");

    // Clear search
    await postsListPage.searchInput.clear();
    await expect(postsListPage.searchInput).toHaveValue("");
  });

  test("should show filter button", async () => {
    // Should show filter button
    await expect(postsListPage.statusFilter).toBeVisible();
    await expect(postsListPage.statusFilter).toContainText("Lọc");

    // Should be clickable
    await postsListPage.statusFilter.click();
  });

  test("should display post data in table", async () => {
    // Should show table headers (use specific selectors)
    await expect(
      postsListPage.page.locator('th:has-text("Tiêu đề")')
    ).toBeVisible();
    await expect(
      postsListPage.page.locator('th:has-text("Trạng thái")')
    ).toBeVisible();
    await expect(
      postsListPage.page.locator('th:has-text("Tác giả")')
    ).toBeVisible();
    await expect(
      postsListPage.page.locator('th:has-text("Ngày tạo")')
    ).toBeVisible();

    // Should show post titles in table
    for (const post of TEST_POSTS) {
      await expect(postsListPage.page.locator("tbody")).toContainText(
        post.title
      );
    }
  });

  test("should show action buttons in table rows", async () => {
    // Each row should have action button (from screenshot, there are buttons in last cell)
    const rows = postsListPage.postRows;
    const count = await rows.count();

    for (let i = 0; i < count; i++) {
      const row = rows.nth(i);
      // Look for button in last cell or any button in row
      const hasButton = (await row.locator("button").count()) > 0;
      expect(hasButton).toBeTruthy();
    }
  });

  test("should navigate to create post page", async () => {
    // Click create button
    await postsListPage.createPostButton.click();

    // Should navigate to create page
    await expect(postsListPage.page).toHaveURL("/admin/posts/create");
  });

  test("should show empty state when no posts", async () => {
    // Delete all posts
    await PostsDbHelper.cleanupTestData();

    // Reload page to see empty state
    await postsListPage.page.reload();
    await postsListPage.waitForPageLoad();

    // Should show empty state
    await expect(postsListPage.emptyState).toBeVisible();
    await expect(
      postsListPage.page.locator("text=Chưa có bài viết nào")
    ).toBeVisible();
  });

  test("should show pagination info", async () => {
    // Should show pagination text (use more specific selectors)
    await expect(
      postsListPage.page.locator("text=Hiển thị").first()
    ).toBeVisible();
    await expect(postsListPage.page.locator("text=Trang 1")).toBeVisible();
  });
});
