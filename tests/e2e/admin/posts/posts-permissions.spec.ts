import { test, expect } from "../../test-setup";
import { PostsListPage } from "../../pages/admin/posts-list.page";
import { PostFormPage } from "../../pages/admin/post-form.page";
import { PostsDbHelper, TEST_POSTS, TEST_CATEGORIES, TEST_ADMIN_USERS } from "../../helpers/posts.helper";

test.describe("Posts Permissions and Authentication", () => {
  let postsListPage: PostsListPage;
  let postFormPage: PostFormPage;
  let testAdmin: any;
  let testModerator: any;
  let testCategories: any[];
  let testPosts: any[];

  test.beforeEach(async ({ page }) => {
    postsListPage = new PostsListPage(page);
    postFormPage = new PostFormPage(page);
    
    // Clean up test data
    await PostsDbHelper.cleanupTestData();
    
    // Create test users
    testAdmin = await PostsDbHelper.createTestAdminUser(TEST_ADMIN_USERS[0]);
    testModerator = await PostsDbHelper.createTestAdminUser({
      ...TEST_ADMIN_USERS[1],
      role: "MODERATOR",
    });
    
    // Create test categories
    testCategories = await PostsDbHelper.createTestCategories();
    
    // Create test posts
    testPosts = await PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id);
  });

  test.afterAll(async () => {
    await PostsDbHelper.cleanupTestData();
  });

  test("should require authentication to access posts management", async ({ page }) => {
    // Try to access posts page without authentication
    await page.goto("/admin/posts");
    
    // Should redirect to login page
    await expect(page).toHaveURL(/\/auth\/signin/);
    await expect(page.locator("text=Đăng nhập")).toBeVisible();
  });

  test("should require authentication for API endpoints", async ({ page }) => {
    // Test GET /api/admin/posts without auth
    const getResponse = await page.request.get("/api/admin/posts");
    expect(getResponse.status()).toBe(401);
    
    const getData = await getResponse.json();
    expect(getData.success).toBe(false);
    expect(getData.error).toContain("Không có quyền truy cập");
    
    // Test POST /api/admin/posts without auth
    const postResponse = await page.request.post("/api/admin/posts", {
      data: {
        title: "Test Post",
        content: "Test content",
      },
    });
    expect(postResponse.status()).toBe(401);
  });

  test("admin should have full access to posts management", async ({ adminPage }) => {
    postsListPage = new PostsListPage(adminPage);
    postFormPage = new PostFormPage(adminPage);
    
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Should be able to view posts
    await postsListPage.expectPostsCount(TEST_POSTS.length);
    
    // Should be able to create posts
    await expect(postsListPage.createPostButton).toBeVisible();
    await postsListPage.clickCreatePost();
    await expect(postFormPage.page).toHaveURL("/admin/posts/create");
    
    // Should be able to edit posts
    await postsListPage.goto();
    await postsListPage.editPostByIndex(0);
    await expect(postFormPage.page).toHaveURL(new RegExp("/admin/posts/.+/edit"));
    
    // Should be able to delete posts
    await postsListPage.goto();
    await postsListPage.deletePostByIndex(0);
    await postsListPage.expectSuccessToast("Xóa bài viết thành công");
  });

  test("moderator should have limited access to posts management", async ({ moderatorPage }) => {
    postsListPage = new PostsListPage(moderatorPage);
    postFormPage = new PostFormPage(moderatorPage);
    
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Should be able to view posts
    await postsListPage.expectPostsCount(TEST_POSTS.length);
    
    // Should be able to create posts
    await expect(postsListPage.createPostButton).toBeVisible();
    
    // Should be able to edit posts
    await postsListPage.editPostByIndex(0);
    await expect(postFormPage.page).toHaveURL(new RegExp("/admin/posts/.+/edit"));
    
    // Should NOT be able to delete posts (admin only)
    await postsListPage.goto();
    
    // Check if delete buttons are hidden for moderators
    const deleteButtons = postsListPage.page.locator('[data-testid="delete-btn"]');
    if (await deleteButtons.count() > 0) {
      // If delete buttons are visible, clicking should show permission error
      await postsListPage.page.click('[data-testid="delete-btn"]');
      await expect(postsListPage.page.locator("text=Không có quyền")).toBeVisible();
    }
  });

  test("admin should be able to delete any post", async ({ adminPage }) => {
    // Test deleting posts via API as admin
    const postToDelete = testPosts[0];
    
    const response = await adminPage.request.delete(`/api/admin/posts/${postToDelete.id}`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
  });

  test("moderator should NOT be able to delete posts", async ({ moderatorPage }) => {
    // Test deleting posts via API as moderator
    const postToDelete = testPosts[0];
    
    const response = await moderatorPage.request.delete(`/api/admin/posts/${postToDelete.id}`);
    expect(response.status()).toBe(403);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Không có quyền truy cập");
  });

  test("admin should be able to bulk delete posts", async ({ adminPage }) => {
    const postsToDelete = testPosts.slice(0, 2);
    const postIds = postsToDelete.map(p => p.id);
    
    const response = await adminPage.request.delete(`/api/admin/posts?action=bulk-delete&ids=${postIds.join(",")}`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.deletedCount).toBe(2);
  });

  test("moderator should NOT be able to bulk delete posts", async ({ moderatorPage }) => {
    const postsToDelete = testPosts.slice(0, 2);
    const postIds = postsToDelete.map(p => p.id);
    
    const response = await moderatorPage.request.delete(`/api/admin/posts?action=bulk-delete&ids=${postIds.join(",")}`);
    expect(response.status()).toBe(403);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Không có quyền truy cập");
  });

  test("users should only see their own posts in edit mode", async ({ adminPage, moderatorPage }) => {
    // Create posts by different authors
    const moderatorPosts = await PostsDbHelper.createTestPosts(testModerator.id, testCategories[0].id);
    
    // Admin should see all posts
    postsListPage = new PostsListPage(adminPage);
    await postsListPage.goto();
    await postsListPage.expectPostsCount(TEST_POSTS.length + moderatorPosts.length);
    
    // Moderator should see all posts but with limited actions
    postsListPage = new PostsListPage(moderatorPage);
    await postsListPage.goto();
    await postsListPage.expectPostsCount(TEST_POSTS.length + moderatorPosts.length);
  });

  test("should validate author permissions on post creation", async ({ adminPage }) => {
    // Try to create post with different author ID
    const newPost = {
      title: "Test Post",
      content: "Test content",
      status: "DRAFT",
      featured: false,
      tags: [],
      authorId: "different-author-id", // This should be overridden
    };
    
    const response = await adminPage.request.post("/api/admin/posts", {
      data: newPost,
    });
    
    expect(response.status()).toBe(201);
    
    const data = await response.json();
    // Author ID should be set to the authenticated user's ID
    expect(data.data.authorId).toBe(testAdmin.id);
  });

  test("should validate author permissions on post update", async ({ adminPage, moderatorPage }) => {
    const postToUpdate = testPosts[0];
    
    // Admin should be able to update any post
    const adminResponse = await adminPage.request.put(`/api/admin/posts/${postToUpdate.id}`, {
      data: { title: "Updated by Admin" },
    });
    expect(adminResponse.status()).toBe(200);
    
    // Moderator should be able to update posts (implementation dependent)
    const moderatorResponse = await moderatorPage.request.put(`/api/admin/posts/${postToUpdate.id}`, {
      data: { title: "Updated by Moderator" },
    });
    
    // This might be 200 (allowed) or 403 (forbidden) depending on business rules
    expect([200, 403]).toContain(moderatorResponse.status());
  });

  test("should handle session expiration", async ({ adminPage }) => {
    postsListPage = new PostsListPage(adminPage);
    
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Simulate session expiration by clearing cookies
    await adminPage.context().clearCookies();
    
    // Try to perform an action that requires authentication
    const response = await adminPage.request.get("/api/admin/posts");
    expect(response.status()).toBe(401);
    
    // UI should handle this gracefully (redirect to login)
    await postsListPage.page.reload();
    await expect(postsListPage.page).toHaveURL(/\/auth\/signin/);
  });

  test("should handle invalid tokens", async ({ page }) => {
    // Set invalid token
    await page.context().addCookies([{
      name: "admin-token",
      value: "invalid-token",
      domain: "localhost",
      path: "/",
    }]);
    
    // Try to access API
    const response = await page.request.get("/api/admin/posts");
    expect(response.status()).toBe(401);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Không có quyền truy cập");
  });

  test("should handle role changes during session", async ({ adminPage }) => {
    // This test simulates a user's role being changed while they have an active session
    // In practice, this would require database manipulation or admin action
    
    postsListPage = new PostsListPage(adminPage);
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Change user role in database (simulate admin demoting user)
    await PostsDbHelper.page.evaluate(async (userId) => {
      // This would require a database update
      // await prisma.adminUser.update({
      //   where: { id: userId },
      //   data: { role: "MODERATOR" }
      // });
    }, testAdmin.id);
    
    // User should still have access until token refresh
    // But new actions should reflect new permissions
    // This test is implementation-dependent
    test.skip("Role change handling not implemented");
  });

  test("should protect against CSRF attacks", async ({ page }) => {
    // This test would check CSRF protection
    // Skip if CSRF protection is not implemented
    test.skip("CSRF protection testing not implemented");
  });

  test("should rate limit API requests", async ({ adminPage }) => {
    // Test rate limiting by making many requests quickly
    const requests = Array.from({ length: 100 }, () =>
      adminPage.request.get("/api/admin/posts")
    );
    
    const responses = await Promise.all(requests);
    
    // Some requests should be rate limited (429 status)
    const rateLimitedResponses = responses.filter(r => r.status() === 429);
    
    // This test depends on rate limiting being implemented
    if (rateLimitedResponses.length === 0) {
      test.skip("Rate limiting not implemented");
    } else {
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    }
  });

  test("should log security events", async ({ adminPage }) => {
    // Test that security events are logged
    // This would require checking logs or audit trail
    
    // Perform actions that should be logged
    await adminPage.request.post("/api/admin/posts", {
      data: {
        title: "Security Test Post",
        content: "Test content",
        status: "DRAFT",
        featured: false,
        tags: [],
      },
    });
    
    // Check if action was logged (implementation dependent)
    // This test is skipped as it requires log checking infrastructure
    test.skip("Security logging testing not implemented");
  });

  test("should handle concurrent authentication", async ({ browser }) => {
    // Test multiple sessions for the same user
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    // Login with same credentials in both contexts
    // This would require implementing login flow
    
    // Both sessions should work independently
    // Or implement session management rules
    
    await context1.close();
    await context2.close();
    
    test.skip("Concurrent authentication testing requires login implementation");
  });

  test("should validate permissions on featured toggle", async ({ adminPage, moderatorPage }) => {
    const postToToggle = testPosts[0];
    
    // Admin should be able to toggle featured
    const adminResponse = await adminPage.request.patch(`/api/admin/posts/${postToToggle.id}?action=toggle-featured`);
    expect(adminResponse.status()).toBe(200);
    
    // Moderator permissions depend on business rules
    const moderatorResponse = await moderatorPage.request.patch(`/api/admin/posts/${postToToggle.id}?action=toggle-featured`);
    expect([200, 403]).toContain(moderatorResponse.status());
  });

  test("should handle permission errors gracefully in UI", async ({ moderatorPage }) => {
    postsListPage = new PostsListPage(moderatorPage);
    
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Try to perform admin-only action
    // UI should either hide the action or show appropriate error
    
    // Check if bulk delete is hidden for moderators
    await postsListPage.selectPostByIndex(0);
    
    const bulkDeleteButton = postsListPage.page.locator('[data-testid="bulk-delete-btn"]');
    if (await bulkDeleteButton.isVisible()) {
      await bulkDeleteButton.click();
      // Should show permission error
      await expect(postsListPage.page.locator("text=Không có quyền")).toBeVisible();
    }
  });
});
