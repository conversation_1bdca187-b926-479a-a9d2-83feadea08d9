import { test, expect } from "../../test-setup";
import { PostsListPage } from "../../pages/admin/posts-list.page";
import { PostsDbHelper, TEST_POSTS, TEST_CATEGORIES, TEST_ADMIN_USERS } from "../../helpers/posts.helper";

test.describe("Posts Stats and Dashboard", () => {
  let postsListPage: PostsListPage;
  let testAdmin: any;
  let testCategories: any[];
  let testPosts: any[];

  test.beforeEach(async ({ adminPage }) => {
    postsListPage = new PostsListPage(adminPage);
    
    // Clean up test data
    await PostsDbHelper.cleanupTestData();
    
    // Create test admin user
    testAdmin = await PostsDbHelper.createTestAdminUser(TEST_ADMIN_USERS[0]);
    
    // Create test categories
    testCategories = await PostsDbHelper.createTestCategories();
    
    // Create test posts
    testPosts = await PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id);
  });

  test.afterAll(async () => {
    await PostsDbHelper.cleanupTestData();
  });

  test("should display posts statistics correctly", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Calculate expected stats from test data
    const totalPosts = TEST_POSTS.length;
    const publishedPosts = TEST_POSTS.filter(p => p.status === "PUBLISHED").length;
    const draftPosts = TEST_POSTS.filter(p => p.status === "DRAFT").length;
    const archivedPosts = TEST_POSTS.filter(p => p.status === "ARCHIVED").length;
    const featuredPosts = TEST_POSTS.filter(p => p.featured).length;
    
    // Check stats cards are visible
    await expect(postsListPage.totalPostsCount).toBeVisible();
    await expect(postsListPage.publishedPostsCount).toBeVisible();
    await expect(postsListPage.draftPostsCount).toBeVisible();
    await expect(postsListPage.featuredPostsCount).toBeVisible();
    
    // Verify stats values
    await expect(postsListPage.totalPostsCount).toContainText(totalPosts.toString());
    await expect(postsListPage.publishedPostsCount).toContainText(publishedPosts.toString());
    await expect(postsListPage.draftPostsCount).toContainText(draftPosts.toString());
    await expect(postsListPage.featuredPostsCount).toContainText(featuredPosts.toString());
  });

  test("should update stats when posts are created", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Get initial stats
    const initialTotal = await postsListPage.totalPostsCount.textContent();
    const initialPublished = await postsListPage.publishedPostsCount.textContent();
    
    // Create a new published post
    await PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id);
    
    // Refresh page to see updated stats
    await postsListPage.page.reload();
    await postsListPage.waitForPageLoad();
    
    // Stats should be updated
    const newTotal = await postsListPage.totalPostsCount.textContent();
    const newPublished = await postsListPage.publishedPostsCount.textContent();
    
    expect(parseInt(newTotal || "0")).toBeGreaterThan(parseInt(initialTotal || "0"));
  });

  test("should update stats when posts are deleted", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Get initial stats
    const initialTotal = await postsListPage.totalPostsCount.textContent();
    
    // Delete a post
    await postsListPage.deletePostByIndex(0);
    
    // Stats should be updated
    const newTotal = await postsListPage.totalPostsCount.textContent();
    expect(parseInt(newTotal || "0")).toBeLessThan(parseInt(initialTotal || "0"));
  });

  test("should update stats when post status changes", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Get initial draft count
    const initialDrafts = await postsListPage.draftPostsCount.textContent();
    const initialPublished = await postsListPage.publishedPostsCount.textContent();
    
    // Find a draft post and change its status to published
    const draftPost = testPosts.find(p => p.status === "DRAFT");
    if (draftPost) {
      // Update post status via API
      await postsListPage.page.evaluate(async (postId) => {
        await fetch(`/api/admin/posts/${postId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status: 'PUBLISHED' })
        });
      }, draftPost.id);
      
      // Refresh page to see updated stats
      await postsListPage.page.reload();
      await postsListPage.waitForPageLoad();
      
      // Stats should be updated
      const newDrafts = await postsListPage.draftPostsCount.textContent();
      const newPublished = await postsListPage.publishedPostsCount.textContent();
      
      expect(parseInt(newDrafts || "0")).toBeLessThan(parseInt(initialDrafts || "0"));
      expect(parseInt(newPublished || "0")).toBeGreaterThan(parseInt(initialPublished || "0"));
    }
  });

  test("should update featured stats when featured status changes", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Get initial featured count
    const initialFeatured = await postsListPage.featuredPostsCount.textContent();
    
    // Toggle featured status of a non-featured post
    const nonFeaturedPost = testPosts.find(p => !p.featured);
    if (nonFeaturedPost) {
      await postsListPage.toggleFeatured(nonFeaturedPost.title);
      
      // Stats should be updated
      const newFeatured = await postsListPage.featuredPostsCount.textContent();
      expect(parseInt(newFeatured || "0")).toBeGreaterThan(parseInt(initialFeatured || "0"));
    }
  });

  test("should display posts stats API endpoint", async () => {
    // Test the stats API endpoint directly
    const response = await postsListPage.page.request.get("/api/admin/posts/stats");
    expect(response.status()).toBe(200);
    
    const stats = await response.json();
    expect(stats.success).toBe(true);
    expect(stats.data).toHaveProperty("total");
    expect(stats.data).toHaveProperty("published");
    expect(stats.data).toHaveProperty("draft");
    expect(stats.data).toHaveProperty("archived");
    expect(stats.data).toHaveProperty("featured");
    
    // Verify stats match database
    const totalFromDb = await PostsDbHelper.getPostsCount();
    const publishedFromDb = await PostsDbHelper.getPostsCount({ status: "PUBLISHED" });
    const draftFromDb = await PostsDbHelper.getPostsCount({ status: "DRAFT" });
    const featuredFromDb = await PostsDbHelper.getPostsCount({ featured: true });
    
    expect(stats.data.total).toBe(totalFromDb);
    expect(stats.data.published).toBe(publishedFromDb);
    expect(stats.data.draft).toBe(draftFromDb);
    expect(stats.data.featured).toBe(featuredFromDb);
  });

  test("should handle stats loading states", async () => {
    // Intercept stats API call to add delay
    await postsListPage.page.route("/api/admin/posts/stats", async (route) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    // Navigate to posts page
    await postsListPage.goto();
    
    // Should show loading state for stats
    await expect(postsListPage.page.locator('[data-testid="stats-loading"]')).toBeVisible();
    
    // Wait for stats to load
    await expect(postsListPage.totalPostsCount).toBeVisible();
    await expect(postsListPage.page.locator('[data-testid="stats-loading"]')).not.toBeVisible();
  });

  test("should handle stats error states", async () => {
    // Intercept stats API call to return error
    await postsListPage.page.route("/api/admin/posts/stats", async (route) => {
      await route.fulfill({
        status: 500,
        contentType: "application/json",
        body: JSON.stringify({ error: "Internal server error" }),
      });
    });
    
    // Navigate to posts page
    await postsListPage.goto();
    
    // Should show error state for stats
    await expect(postsListPage.page.locator('[data-testid="stats-error"]')).toBeVisible();
    await expect(postsListPage.page.locator("text=Không thể tải thống kê")).toBeVisible();
  });

  test("should display quick actions for posts", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Check quick actions are visible
    await expect(postsListPage.page.locator('[data-testid="quick-actions"]')).toBeVisible();
    
    // Check individual quick action buttons
    await expect(postsListPage.page.locator('[data-testid="quick-create-post"]')).toBeVisible();
    await expect(postsListPage.page.locator('[data-testid="quick-view-drafts"]')).toBeVisible();
    await expect(postsListPage.page.locator('[data-testid="quick-view-published"]')).toBeVisible();
  });

  test("should navigate via quick actions", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Click quick create post action
    await postsListPage.page.click('[data-testid="quick-create-post"]');
    await expect(postsListPage.page).toHaveURL("/admin/posts/create");
    
    // Go back to posts list
    await postsListPage.goto();
    
    // Click quick view drafts action
    await postsListPage.page.click('[data-testid="quick-view-drafts"]');
    
    // Should filter to show only drafts
    const draftPosts = TEST_POSTS.filter(p => p.status === "DRAFT");
    await postsListPage.expectPostsCount(draftPosts.length);
  });

  test("should display recent activity", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Check recent activity section
    await expect(postsListPage.page.locator('[data-testid="recent-activity"]')).toBeVisible();
    await expect(postsListPage.page.locator("text=Hoạt động gần đây")).toBeVisible();
    
    // Should show recent post activities
    await expect(postsListPage.page.locator('[data-testid="activity-item"]')).toHaveCount.toBeGreaterThan(0);
  });

  test("should show posts by author stats", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Check author stats section (if implemented)
    const authorStats = postsListPage.page.locator('[data-testid="author-stats"]');
    if (await authorStats.isVisible()) {
      await expect(authorStats).toBeVisible();
      await expect(postsListPage.page.locator(`text=${testAdmin.name}`)).toBeVisible();
      await expect(postsListPage.page.locator(`text=${TEST_POSTS.length}`)).toBeVisible();
    }
  });

  test("should show posts by category stats", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Check category stats section (if implemented)
    const categoryStats = postsListPage.page.locator('[data-testid="category-stats"]');
    if (await categoryStats.isVisible()) {
      await expect(categoryStats).toBeVisible();
      await expect(postsListPage.page.locator(`text=${testCategories[0].name}`)).toBeVisible();
    }
  });

  test("should display posts performance metrics", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Check performance metrics (if implemented)
    const performanceMetrics = postsListPage.page.locator('[data-testid="performance-metrics"]');
    if (await performanceMetrics.isVisible()) {
      await expect(performanceMetrics).toBeVisible();
      
      // Check for metrics like views, engagement, etc.
      await expect(postsListPage.page.locator('[data-testid="total-views"]')).toBeVisible();
      await expect(postsListPage.page.locator('[data-testid="avg-engagement"]')).toBeVisible();
    }
  });

  test("should refresh stats when page is refreshed", async () => {
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Get initial stats
    const initialTotal = await postsListPage.totalPostsCount.textContent();
    
    // Create a new post via API
    await postsListPage.page.evaluate(async (adminId, categoryId) => {
      await fetch('/api/admin/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: 'New Test Post',
          content: 'Test content',
          status: 'PUBLISHED',
          featured: false,
          tags: [],
          authorId: adminId,
          categoryId: categoryId
        })
      });
    }, testAdmin.id, testCategories[0].id);
    
    // Refresh page
    await postsListPage.page.reload();
    await postsListPage.waitForPageLoad();
    
    // Stats should be updated
    const newTotal = await postsListPage.totalPostsCount.textContent();
    expect(parseInt(newTotal || "0")).toBeGreaterThan(parseInt(initialTotal || "0"));
  });

  test("should handle empty stats gracefully", async () => {
    // Delete all posts
    await PostsDbHelper.cleanupTestData();
    
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // All stats should show 0
    await expect(postsListPage.totalPostsCount).toContainText("0");
    await expect(postsListPage.publishedPostsCount).toContainText("0");
    await expect(postsListPage.draftPostsCount).toContainText("0");
    await expect(postsListPage.featuredPostsCount).toContainText("0");
    
    // Should show empty state
    await postsListPage.expectEmptyState();
  });

  test("should display stats with proper formatting", async () => {
    // Create many posts to test number formatting
    const manyPosts = Array.from({ length: 1500 }, (_, i) => ({
      title: `Test Post ${i + 1}`,
      content: `Content for test post ${i + 1}`,
      status: "PUBLISHED" as const,
      featured: i % 10 === 0, // Every 10th post is featured
      tags: [],
      authorId: testAdmin.id,
      categoryId: testCategories[0].id,
    }));
    
    // Create posts in batches to avoid timeout
    for (let i = 0; i < manyPosts.length; i += 100) {
      const batch = manyPosts.slice(i, i + 100);
      await Promise.all(batch.map(post => 
        PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id)
      ));
    }
    
    // Navigate to posts page
    await postsListPage.goto();
    await postsListPage.waitForPageLoad();
    
    // Check if large numbers are formatted properly (e.g., 1,500 or 1.5K)
    const totalText = await postsListPage.totalPostsCount.textContent();
    expect(totalText).toMatch(/1[,.]?5\d{2}|1\.5K/); // Should handle large number formatting
  });
});
