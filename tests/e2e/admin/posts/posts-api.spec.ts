import { test, expect } from "../../test-setup";
import { PostsDbHelper, TEST_POSTS, TEST_CATEGORIES, TEST_ADMIN_USERS } from "../../helpers/posts.helper";

test.describe("Posts API Endpoints", () => {
  let testAdmin: any;
  let testCategories: any[];
  let testPosts: any[];

  test.beforeEach(async ({ adminPage }) => {
    // Clean up test data
    await PostsDbHelper.cleanupTestData();
    
    // Create test admin user
    testAdmin = await PostsDbHelper.createTestAdminUser(TEST_ADMIN_USERS[0]);
    
    // Create test categories
    testCategories = await PostsDbHelper.createTestCategories();
    
    // Create test posts
    testPosts = await PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id);
  });

  test.afterAll(async () => {
    await PostsDbHelper.cleanupTestData();
  });

  test("GET /api/admin/posts should return posts list", async ({ adminPage }) => {
    const response = await adminPage.request.get("/api/admin/posts");
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty("posts");
    expect(data.data).toHaveProperty("pagination");
    expect(data.data.posts).toHaveLength(TEST_POSTS.length);
    
    // Check post structure
    const post = data.data.posts[0];
    expect(post).toHaveProperty("id");
    expect(post).toHaveProperty("title");
    expect(post).toHaveProperty("content");
    expect(post).toHaveProperty("status");
    expect(post).toHaveProperty("featured");
    expect(post).toHaveProperty("author");
    expect(post).toHaveProperty("createdAt");
  });

  test("GET /api/admin/posts should support pagination", async ({ adminPage }) => {
    const response = await adminPage.request.get("/api/admin/posts?page=1&limit=2");
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.data.posts).toHaveLength(2);
    expect(data.data.pagination.page).toBe(1);
    expect(data.data.pagination.limit).toBe(2);
    expect(data.data.pagination.total).toBe(TEST_POSTS.length);
  });

  test("GET /api/admin/posts should support search", async ({ adminPage }) => {
    const response = await adminPage.request.get("/api/admin/posts?search=Hướng dẫn");
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.data.posts.length).toBeGreaterThan(0);
    
    // All returned posts should match search term
    data.data.posts.forEach((post: any) => {
      expect(post.title.toLowerCase()).toContain("hướng dẫn");
    });
  });

  test("GET /api/admin/posts should support status filter", async ({ adminPage }) => {
    const response = await adminPage.request.get("/api/admin/posts?status=PUBLISHED");
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const publishedPosts = TEST_POSTS.filter(p => p.status === "PUBLISHED");
    expect(data.data.posts).toHaveLength(publishedPosts.length);
    
    // All returned posts should be published
    data.data.posts.forEach((post: any) => {
      expect(post.status).toBe("PUBLISHED");
    });
  });

  test("GET /api/admin/posts should support featured filter", async ({ adminPage }) => {
    const response = await adminPage.request.get("/api/admin/posts?featured=true");
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const featuredPosts = TEST_POSTS.filter(p => p.featured);
    expect(data.data.posts).toHaveLength(featuredPosts.length);
    
    // All returned posts should be featured
    data.data.posts.forEach((post: any) => {
      expect(post.featured).toBe(true);
    });
  });

  test("GET /api/admin/posts should support sorting", async ({ adminPage }) => {
    const response = await adminPage.request.get("/api/admin/posts?sortBy=title&sortOrder=asc");
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const titles = data.data.posts.map((post: any) => post.title);
    const sortedTitles = [...titles].sort();
    expect(titles).toEqual(sortedTitles);
  });

  test("POST /api/admin/posts should create new post", async ({ adminPage }) => {
    const newPost = {
      title: "New Test Post",
      content: "This is a new test post content",
      excerpt: "Test excerpt",
      status: "DRAFT",
      featured: false,
      tags: ["test", "new"],
      categoryId: testCategories[0].id,
    };
    
    const response = await adminPage.request.post("/api/admin/posts", {
      data: newPost,
    });
    
    expect(response.status()).toBe(201);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty("id");
    expect(data.data.title).toBe(newPost.title);
    expect(data.data.content).toBe(newPost.content);
    expect(data.data.status).toBe(newPost.status);
    expect(data.data.featured).toBe(newPost.featured);
    expect(data.data.tags).toEqual(newPost.tags);
    
    // Verify in database
    const createdPost = await PostsDbHelper.getPostBySlug("new-test-post");
    expect(createdPost).toBeTruthy();
  });

  test("POST /api/admin/posts should validate required fields", async ({ adminPage }) => {
    const invalidPost = {
      content: "Content without title",
    };
    
    const response = await adminPage.request.post("/api/admin/posts", {
      data: invalidPost,
    });
    
    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Tiêu đề là bắt buộc");
  });

  test("POST /api/admin/posts should auto-generate slug", async ({ adminPage }) => {
    const newPost = {
      title: "Post Without Slug",
      content: "This post doesn't have a slug",
      status: "DRAFT",
      featured: false,
      tags: [],
    };
    
    const response = await adminPage.request.post("/api/admin/posts", {
      data: newPost,
    });
    
    expect(response.status()).toBe(201);
    
    const data = await response.json();
    expect(data.data.slug).toBe("post-without-slug");
  });

  test("GET /api/admin/posts/[id] should return single post", async ({ adminPage }) => {
    const postToGet = testPosts[0];
    
    const response = await adminPage.request.get(`/api/admin/posts/${postToGet.id}`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.id).toBe(postToGet.id);
    expect(data.data.title).toBe(postToGet.title);
    expect(data.data.content).toBe(postToGet.content);
  });

  test("GET /api/admin/posts/[id] should return 404 for non-existent post", async ({ adminPage }) => {
    const response = await adminPage.request.get("/api/admin/posts/non-existent-id");
    
    expect(response.status()).toBe(404);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Không tìm thấy bài viết");
  });

  test("PUT /api/admin/posts/[id] should update post", async ({ adminPage }) => {
    const postToUpdate = testPosts[0];
    const updates = {
      title: "Updated Post Title",
      content: "Updated content",
      status: "PUBLISHED",
      featured: true,
    };
    
    const response = await adminPage.request.put(`/api/admin/posts/${postToUpdate.id}`, {
      data: updates,
    });
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.title).toBe(updates.title);
    expect(data.data.content).toBe(updates.content);
    expect(data.data.status).toBe(updates.status);
    expect(data.data.featured).toBe(updates.featured);
    
    // Verify in database
    const updatedPost = await PostsDbHelper.getPostBySlug(postToUpdate.slug);
    expect(updatedPost?.title).toBe(updates.title);
  });

  test("PATCH /api/admin/posts/[id] should toggle featured status", async ({ adminPage }) => {
    const postToToggle = testPosts[0];
    const originalFeatured = postToToggle.featured;
    
    const response = await adminPage.request.patch(`/api/admin/posts/${postToToggle.id}?action=toggle-featured`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.featured).toBe(!originalFeatured);
    
    // Verify in database
    const updatedPost = await PostsDbHelper.getPostBySlug(postToToggle.slug);
    expect(updatedPost?.featured).toBe(!originalFeatured);
  });

  test("DELETE /api/admin/posts/[id] should delete post", async ({ adminPage }) => {
    const postToDelete = testPosts[0];
    
    const response = await adminPage.request.delete(`/api/admin/posts/${postToDelete.id}`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    
    // Verify post is deleted from database
    const deletedPost = await PostsDbHelper.getPostBySlug(postToDelete.slug);
    expect(deletedPost).toBeNull();
  });

  test("DELETE /api/admin/posts should bulk delete posts", async ({ adminPage }) => {
    const postsToDelete = testPosts.slice(0, 2);
    const postIds = postsToDelete.map(p => p.id);
    
    const response = await adminPage.request.delete(`/api/admin/posts?action=bulk-delete&ids=${postIds.join(",")}`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.deletedCount).toBe(2);
    
    // Verify posts are deleted from database
    for (const post of postsToDelete) {
      const deletedPost = await PostsDbHelper.getPostBySlug(post.slug);
      expect(deletedPost).toBeNull();
    }
  });

  test("PATCH /api/admin/posts should bulk update status", async ({ adminPage }) => {
    const postsToUpdate = testPosts.slice(0, 2);
    const postIds = postsToUpdate.map(p => p.id);
    
    const response = await adminPage.request.patch(`/api/admin/posts?action=bulk-update&ids=${postIds.join(",")}&status=PUBLISHED`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.updatedCount).toBe(2);
    
    // Verify posts are updated in database
    for (const post of postsToUpdate) {
      const updatedPost = await PostsDbHelper.getPostBySlug(post.slug);
      expect(updatedPost?.status).toBe("PUBLISHED");
    }
  });

  test("GET /api/admin/posts/stats should return statistics", async ({ adminPage }) => {
    const response = await adminPage.request.get("/api/admin/posts/stats");
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty("total");
    expect(data.data).toHaveProperty("published");
    expect(data.data).toHaveProperty("draft");
    expect(data.data).toHaveProperty("archived");
    expect(data.data).toHaveProperty("featured");
    
    // Verify stats match test data
    const publishedCount = TEST_POSTS.filter(p => p.status === "PUBLISHED").length;
    const draftCount = TEST_POSTS.filter(p => p.status === "DRAFT").length;
    const featuredCount = TEST_POSTS.filter(p => p.featured).length;
    
    expect(data.data.total).toBe(TEST_POSTS.length);
    expect(data.data.published).toBe(publishedCount);
    expect(data.data.draft).toBe(draftCount);
    expect(data.data.featured).toBe(featuredCount);
  });

  test("should handle authentication errors", async ({ page }) => {
    // Make request without authentication
    const response = await page.request.get("/api/admin/posts");
    
    expect(response.status()).toBe(401);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Không có quyền truy cập");
  });

  test("should handle invalid JSON in POST request", async ({ adminPage }) => {
    const response = await adminPage.request.post("/api/admin/posts", {
      data: "invalid json",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    expect(response.status()).toBe(400);
  });

  test("should handle server errors gracefully", async ({ adminPage }) => {
    // This test would require mocking database errors
    // Skip for now as it requires more complex setup
    test.skip();
  });

  test("should validate slug uniqueness", async ({ adminPage }) => {
    const existingPost = testPosts[0];
    
    const newPost = {
      title: "Different Title",
      content: "Different content",
      slug: existingPost.slug, // Use existing slug
      status: "DRAFT",
      featured: false,
      tags: [],
    };
    
    const response = await adminPage.request.post("/api/admin/posts", {
      data: newPost,
    });
    
    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Slug đã tồn tại");
  });

  test("should handle category validation", async ({ adminPage }) => {
    const newPost = {
      title: "Post with Invalid Category",
      content: "Test content",
      status: "DRAFT",
      featured: false,
      tags: [],
      categoryId: "non-existent-category-id",
    };
    
    const response = await adminPage.request.post("/api/admin/posts", {
      data: newPost,
    });
    
    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Danh mục không tồn tại");
  });

  test("should handle author validation", async ({ adminPage }) => {
    const newPost = {
      title: "Post with Invalid Author",
      content: "Test content",
      status: "DRAFT",
      featured: false,
      tags: [],
      authorId: "non-existent-author-id",
    };
    
    const response = await adminPage.request.post("/api/admin/posts", {
      data: newPost,
    });
    
    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain("Tác giả không tồn tại");
  });

  test("should handle concurrent updates", async ({ adminPage }) => {
    const postToUpdate = testPosts[0];
    
    // Simulate concurrent updates
    const update1 = adminPage.request.put(`/api/admin/posts/${postToUpdate.id}`, {
      data: { title: "Update 1" },
    });
    
    const update2 = adminPage.request.put(`/api/admin/posts/${postToUpdate.id}`, {
      data: { title: "Update 2" },
    });
    
    const [response1, response2] = await Promise.all([update1, update2]);
    
    // Both should succeed (last write wins)
    expect(response1.status()).toBe(200);
    expect(response2.status()).toBe(200);
    
    // Verify final state
    const finalPost = await PostsDbHelper.getPostBySlug(postToUpdate.slug);
    expect(finalPost?.title).toMatch(/Update [12]/);
  });

  test("should handle large content", async ({ adminPage }) => {
    const largeContent = "A".repeat(100000); // 100KB content
    
    const newPost = {
      title: "Post with Large Content",
      content: largeContent,
      status: "DRAFT",
      featured: false,
      tags: [],
    };
    
    const response = await adminPage.request.post("/api/admin/posts", {
      data: newPost,
    });
    
    expect(response.status()).toBe(201);
    
    const data = await response.json();
    expect(data.data.content).toBe(largeContent);
  });
});
