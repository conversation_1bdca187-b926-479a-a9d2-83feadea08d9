import { test, expect } from "../../test-setup";
import { PostFormPage } from "../../pages/admin/post-form.page";
import { PostsListPage } from "../../pages/admin/posts-list.page";
import { PostsDbHelper, TEST_POSTS, TEST_CATEGORIES, TEST_ADMIN_USERS } from "../../helpers/posts.helper";

test.describe("Edit Post Page", () => {
  let postFormPage: PostFormPage;
  let postsListPage: PostsListPage;
  let testAdmin: any;
  let testCategories: any[];
  let testPosts: any[];

  test.beforeEach(async ({ adminPage }) => {
    postFormPage = new PostFormPage(adminPage);
    postsListPage = new PostsListPage(adminPage);
    
    // Clean up test data
    await PostsDbHelper.cleanupTestData();
    
    // Create test admin user
    testAdmin = await PostsDbHelper.createTestAdminUser(TEST_ADMIN_USERS[0]);
    
    // Create test categories
    testCategories = await PostsDbHelper.createTestCategories();
    
    // Create test posts
    testPosts = await PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id);
  });

  test.afterAll(async () => {
    await PostsDbHelper.cleanupTestData();
  });

  test("should display edit post page correctly", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Check page title
    await postFormPage.expectEditPageTitle();
    await expect(postFormPage.backButton).toBeVisible();
    
    // Check form is populated with existing data
    expect(await postFormPage.getTitleValue()).toBe(postToEdit.title);
    expect(await postFormPage.getSlugValue()).toBe(postToEdit.slug);
    expect(await postFormPage.getExcerptValue()).toBe(postToEdit.excerpt || "");
    expect(await postFormPage.isFeatured()).toBe(postToEdit.featured);
    
    // Check content is loaded
    const contentValue = await postFormPage.getContentValue();
    expect(contentValue).toContain("Việc chọn size"); // Part of the test content
  });

  test("should update post title and slug", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Update title
    const newTitle = "Updated Post Title";
    await postFormPage.fillTitle(newTitle);
    
    // Update slug
    const newSlug = "updated-post-slug";
    await postFormPage.fillSlug(newSlug);
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify changes in database
    const updatedPost = await PostsDbHelper.getPostBySlug(newSlug);
    expect(updatedPost?.title).toBe(newTitle);
    expect(updatedPost?.slug).toBe(newSlug);
  });

  test("should update post content", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Update content
    const newContent = "<p>This is the updated content for the post.</p>";
    await postFormPage.fillContentHTML(newContent);
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify content in database
    const updatedPost = await PostsDbHelper.getPostBySlug(postToEdit.slug);
    expect(updatedPost?.content).toContain("updated content");
  });

  test("should update post status", async () => {
    const draftPost = testPosts.find(p => p.status === "DRAFT");
    if (!draftPost) {
      test.skip("No draft post available for testing");
      return;
    }
    
    // Navigate to edit page
    await postFormPage.gotoEdit(draftPost.id);
    await postFormPage.waitForPageLoad();
    
    // Change status to published
    await postFormPage.selectStatus("PUBLISHED");
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify status in database
    const updatedPost = await PostsDbHelper.getPostBySlug(draftPost.slug);
    expect(updatedPost?.status).toBe("PUBLISHED");
  });

  test("should toggle featured status", async () => {
    const postToEdit = testPosts[0];
    const originalFeatured = postToEdit.featured;
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Toggle featured status
    await postFormPage.toggleFeatured(!originalFeatured);
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify featured status in database
    const updatedPost = await PostsDbHelper.getPostBySlug(postToEdit.slug);
    expect(updatedPost?.featured).toBe(!originalFeatured);
  });

  test("should update post category", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Change category
    await postFormPage.selectCategory(testCategories[1].name);
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify category in database
    const updatedPost = await PostsDbHelper.getPostBySlug(postToEdit.slug);
    expect(updatedPost?.categoryId).toBe(testCategories[1].id);
  });

  test("should update post tags", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Remove existing tags (if any)
    const existingTags = await postFormPage.getSelectedTags();
    for (const tag of existingTags) {
      await postFormPage.removeTag(tag);
    }
    
    // Add new tags
    const newTags = ["updated", "edited", "test"];
    await postFormPage.addTags(newTags);
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify tags in database
    const updatedPost = await PostsDbHelper.getPostBySlug(postToEdit.slug);
    expect(updatedPost?.tags).toEqual(expect.arrayContaining(newTags));
  });

  test("should update excerpt", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Update excerpt
    const newExcerpt = "This is an updated excerpt for the post.";
    await postFormPage.fillExcerpt(newExcerpt);
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify excerpt in database
    const updatedPost = await PostsDbHelper.getPostBySlug(postToEdit.slug);
    expect(updatedPost?.excerpt).toBe(newExcerpt);
  });

  test("should update featured image", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Set new featured image
    const newImageUrl = "https://example.com/new-featured-image.jpg";
    await postFormPage.setFeaturedImage(newImageUrl);
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify image in database
    const updatedPost = await PostsDbHelper.getPostBySlug(postToEdit.slug);
    expect(updatedPost?.featuredImage).toBe(newImageUrl);
  });

  test("should remove featured image", async () => {
    // First create a post with featured image
    const postWithImage = testPosts.find(p => p.featuredImage);
    if (!postWithImage) {
      // Create one for testing
      const newPost = await PostsDbHelper.createTestPosts(testAdmin.id, testCategories[0].id);
      // Update it to have featured image
      await postFormPage.page.evaluate(async (postId) => {
        await fetch(`/api/admin/posts/${postId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ featuredImage: 'https://example.com/test.jpg' })
        });
      }, newPost[0].id);
    }
    
    const postToEdit = postWithImage || testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Remove featured image
    await postFormPage.removeFeaturedImage();
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify image is removed
    const updatedPost = await PostsDbHelper.getPostBySlug(postToEdit.slug);
    expect(updatedPost?.featuredImage).toBeFalsy();
  });

  test("should validate required fields on update", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Clear required fields
    await postFormPage.fillTitle("");
    await postFormPage.fillContent("");
    
    // Try to save
    await postFormPage.save();
    
    // Should show validation errors
    await postFormPage.expectTitleError("Tiêu đề là bắt buộc");
    await postFormPage.expectContentError("Nội dung là bắt buộc");
  });

  test("should handle slug uniqueness on update", async () => {
    const postToEdit = testPosts[0];
    const anotherPost = testPosts[1];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Try to use another post's slug
    await postFormPage.fillSlug(anotherPost.slug);
    
    // Try to save
    await postFormPage.save();
    
    // Should show slug error
    await postFormPage.expectSlugError("Slug đã tồn tại");
  });

  test("should allow keeping the same slug", async () => {
    const postToEdit = testPosts[0];
    const originalSlug = postToEdit.slug;
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Update title but keep same slug
    await postFormPage.fillTitle("Updated Title");
    await postFormPage.fillSlug(originalSlug);
    
    // Save changes
    await postFormPage.save();
    
    // Should save successfully
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify slug remains the same
    const updatedPost = await PostsDbHelper.getPostBySlug(originalSlug);
    expect(updatedPost?.slug).toBe(originalSlug);
    expect(updatedPost?.title).toBe("Updated Title");
  });

  test("should handle preview functionality", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Click preview button
    await postFormPage.preview();
    
    // Should open preview in new tab (implementation dependent)
    // This test might need adjustment based on actual preview implementation
  });

  test("should handle form cancellation", async () => {
    const postToEdit = testPosts[0];
    const originalTitle = postToEdit.title;
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Make changes
    await postFormPage.fillTitle("Changed Title");
    
    // Cancel changes
    await postFormPage.cancel();
    
    // Should redirect to posts list
    await postFormPage.expectRedirectToPostsList();
    
    // Changes should not be saved
    const unchangedPost = await PostsDbHelper.getPostBySlug(postToEdit.slug);
    expect(unchangedPost?.title).toBe(originalTitle);
  });

  test("should handle back navigation", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Click back button
    await postFormPage.goBack();
    
    // Should redirect to posts list
    await postFormPage.expectRedirectToPostsList();
  });

  test("should show loading state during update", async () => {
    const postToEdit = testPosts[0];
    
    // Intercept API call to add delay
    await postFormPage.page.route(`/api/admin/posts/${postToEdit.id}`, async (route) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Make changes and save
    await postFormPage.fillTitle("Updated Title");
    await postFormPage.save();
    
    // Should show saving state
    await postFormPage.expectSavingState();
    
    // Form should be disabled during save
    await postFormPage.expectFormDisabled();
  });

  test("should handle update errors", async () => {
    const postToEdit = testPosts[0];
    
    // Intercept API call to return error
    await postFormPage.page.route(`/api/admin/posts/${postToEdit.id}`, async (route) => {
      await route.fulfill({
        status: 400,
        contentType: "application/json",
        body: JSON.stringify({ 
          success: false, 
          error: "Có lỗi xảy ra khi cập nhật bài viết" 
        }),
      });
    });
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Make changes and save
    await postFormPage.fillTitle("Updated Title");
    await postFormPage.save();
    
    // Should show error message
    await postFormPage.expectErrorMessage("Có lỗi xảy ra khi cập nhật bài viết");
    
    // Form should remain enabled
    await postFormPage.expectFormEnabled();
  });

  test("should handle post not found", async () => {
    const nonExistentId = "non-existent-post-id";
    
    // Try to navigate to edit page with non-existent ID
    await postFormPage.gotoEdit(nonExistentId);
    
    // Should show error or redirect
    // Implementation dependent - might show 404 page or redirect to list
    await expect(postFormPage.page.locator("text=Không tìm thấy bài viết")).toBeVisible();
  });

  test("should preserve unsaved changes warning", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Make changes without saving
    await postFormPage.fillTitle("Unsaved Changes");
    
    // Try to navigate away
    await postFormPage.goBack();
    
    // Should show unsaved changes warning (if implemented)
    // This test depends on actual implementation
    test.skip();
  });

  test("should update multiple fields at once", async () => {
    const postToEdit = testPosts[0];
    
    // Navigate to edit page
    await postFormPage.gotoEdit(postToEdit.id);
    await postFormPage.waitForPageLoad();
    
    // Update multiple fields
    const updates = {
      title: "Completely Updated Post",
      content: "<p>This is completely new content.</p>",
      excerpt: "New excerpt",
      status: "PUBLISHED" as const,
      featured: !postToEdit.featured,
      category: testCategories[1].name,
      tags: ["updated", "multiple", "fields"],
    };
    
    await postFormPage.fillPostForm(updates);
    
    // Save changes
    await postFormPage.save();
    
    // Should show success message
    await postFormPage.expectSuccessMessage("Cập nhật bài viết thành công");
    
    // Verify all changes in database
    const updatedPost = await PostsDbHelper.getPostBySlug("completely-updated-post");
    expect(updatedPost?.title).toBe(updates.title);
    expect(updatedPost?.excerpt).toBe(updates.excerpt);
    expect(updatedPost?.status).toBe(updates.status);
    expect(updatedPost?.featured).toBe(updates.featured);
    expect(updatedPost?.categoryId).toBe(testCategories[1].id);
    expect(updatedPost?.tags).toEqual(expect.arrayContaining(updates.tags));
  });
});
