import { Page, Locator, expect } from "@playwright/test";

export class PostFormPage {
  readonly page: Page;
  
  // Header elements
  readonly pageTitle: Locator;
  readonly pageDescription: Locator;
  readonly backButton: Locator;
  
  // Form elements - Basic info
  readonly titleInput: Locator;
  readonly titleError: Locator;
  readonly slugInput: Locator;
  readonly slugError: Locator;
  readonly excerptTextarea: Locator;
  readonly excerptError: Locator;
  
  // Content editor
  readonly contentEditor: Locator;
  readonly contentEditorToolbar: Locator;
  readonly contentError: Locator;
  
  // Sidebar elements
  readonly statusSelect: Locator;
  readonly featuredSwitch: Locator;
  readonly categorySelect: Locator;
  readonly categoryError: Locator;
  
  // Tags
  readonly tagInput: Locator;
  readonly tagsList: Locator;
  readonly addTagButton: Locator;
  
  // Featured image
  readonly featuredImageButton: Locator;
  readonly featuredImagePreview: Locator;
  readonly removeFeaturedImageButton: Locator;
  
  // SEO section
  readonly metaTitleInput: Locator;
  readonly metaDescriptionTextarea: Locator;
  
  // Action buttons
  readonly saveButton: Locator;
  readonly saveDraftButton: Locator;
  readonly publishButton: Locator;
  readonly previewButton: Locator;
  readonly cancelButton: Locator;
  
  // Loading states
  readonly loadingSpinner: Locator;
  readonly savingIndicator: Locator;
  
  // Dialogs
  readonly mediaDialog: Locator;
  readonly unsavedChangesDialog: Locator;
  readonly confirmLeaveButton: Locator;
  readonly cancelLeaveButton: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Header elements
    this.pageTitle = page.locator('h1');
    this.pageDescription = page.locator('[data-testid="page-description"]');
    this.backButton = page.locator('[data-testid="back-btn"], a:has-text("Quay lại")');
    
    // Form elements - Basic info
    this.titleInput = page.locator('[data-testid="title-input"], input[name="title"]');
    this.titleError = page.locator('[data-testid="title-error"]');
    this.slugInput = page.locator('[data-testid="slug-input"], input[name="slug"]');
    this.slugError = page.locator('[data-testid="slug-error"]');
    this.excerptTextarea = page.locator('[data-testid="excerpt-input"], textarea[name="excerpt"]');
    this.excerptError = page.locator('[data-testid="excerpt-error"]');
    
    // Content editor
    this.contentEditor = page.locator('.ql-editor, [data-testid="content-editor"]');
    this.contentEditorToolbar = page.locator('.ql-toolbar');
    this.contentError = page.locator('[data-testid="content-error"]');
    
    // Sidebar elements
    this.statusSelect = page.locator('[data-testid="status-select"]');
    this.featuredSwitch = page.locator('[data-testid="featured-switch"]');
    this.categorySelect = page.locator('[data-testid="category-select"]');
    this.categoryError = page.locator('[data-testid="category-error"]');
    
    // Tags
    this.tagInput = page.locator('[data-testid="tag-input"]');
    this.tagsList = page.locator('[data-testid="tags-list"]');
    this.addTagButton = page.locator('[data-testid="add-tag-btn"]');
    
    // Featured image
    this.featuredImageButton = page.locator('[data-testid="featured-image-btn"]');
    this.featuredImagePreview = page.locator('[data-testid="featured-image-preview"]');
    this.removeFeaturedImageButton = page.locator('[data-testid="remove-featured-image-btn"]');
    
    // SEO section
    this.metaTitleInput = page.locator('[data-testid="meta-title-input"]');
    this.metaDescriptionTextarea = page.locator('[data-testid="meta-description-input"]');
    
    // Action buttons
    this.saveButton = page.locator('[data-testid="save-btn"], button:has-text("Lưu"), button:has-text("Cập nhật")');
    this.saveDraftButton = page.locator('[data-testid="save-draft-btn"]');
    this.publishButton = page.locator('[data-testid="publish-btn"]');
    this.previewButton = page.locator('[data-testid="preview-btn"]');
    this.cancelButton = page.locator('[data-testid="cancel-btn"], button:has-text("Hủy")');
    
    // Loading states
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]');
    this.savingIndicator = page.locator('[data-testid="saving-indicator"]');
    
    // Dialogs
    this.mediaDialog = page.locator('[data-testid="media-dialog"]');
    this.unsavedChangesDialog = page.locator('[data-testid="unsaved-changes-dialog"]');
    this.confirmLeaveButton = page.locator('[data-testid="confirm-leave-btn"]');
    this.cancelLeaveButton = page.locator('[data-testid="cancel-leave-btn"]');
  }

  async gotoCreate() {
    await this.page.goto("/admin/posts/create");
    await this.page.waitForLoadState("networkidle");
  }

  async gotoEdit(postId: string) {
    await this.page.goto(`/admin/posts/${postId}/edit`);
    await this.page.waitForLoadState("networkidle");
  }

  async waitForPageLoad() {
    await expect(this.pageTitle).toBeVisible();
    await this.page.waitForLoadState("networkidle");
  }

  // Form filling methods
  async fillTitle(title: string) {
    await this.titleInput.fill(title);
    // Wait for slug auto-generation if it's a create form
    await this.page.waitForTimeout(500);
  }

  async fillSlug(slug: string) {
    await this.slugInput.fill(slug);
  }

  async fillExcerpt(excerpt: string) {
    await this.excerptTextarea.fill(excerpt);
  }

  async fillContent(content: string) {
    // Wait for Quill editor to be ready
    await this.page.waitForSelector('.ql-editor');
    await this.contentEditor.fill(content);
  }

  async fillContentHTML(html: string) {
    // For rich content, we might need to use innerHTML
    await this.page.waitForSelector('.ql-editor');
    await this.page.evaluate((html) => {
      const editor = document.querySelector('.ql-editor');
      if (editor) {
        editor.innerHTML = html;
      }
    }, html);
  }

  async selectStatus(status: "DRAFT" | "PUBLISHED" | "ARCHIVED") {
    await this.statusSelect.click();
    await this.page.click(`text=${status}`);
  }

  async toggleFeatured(featured: boolean = true) {
    const isChecked = await this.featuredSwitch.isChecked();
    if ((featured && !isChecked) || (!featured && isChecked)) {
      await this.featuredSwitch.click();
    }
  }

  async selectCategory(categoryName: string) {
    await this.categorySelect.click();
    await this.page.click(`text=${categoryName}`);
  }

  async addTag(tag: string) {
    await this.tagInput.fill(tag);
    await this.tagInput.press("Enter");
  }

  async addTags(tags: string[]) {
    for (const tag of tags) {
      await this.addTag(tag);
    }
  }

  async removeTag(tag: string) {
    const tagElement = this.page.locator(`[data-testid="tag-item"]:has-text("${tag}")`);
    await tagElement.locator('[data-testid="remove-tag-btn"]').click();
  }

  async setFeaturedImage(imageUrl: string) {
    await this.featuredImageButton.click();
    // Assuming media dialog opens
    await this.page.fill('[data-testid="image-url-input"]', imageUrl);
    await this.page.click('[data-testid="select-image-btn"]');
  }

  async removeFeaturedImage() {
    await this.removeFeaturedImageButton.click();
  }

  async fillMetaTitle(title: string) {
    await this.metaTitleInput.fill(title);
  }

  async fillMetaDescription(description: string) {
    await this.metaDescriptionTextarea.fill(description);
  }

  // Form submission methods
  async save() {
    await this.saveButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async saveAsDraft() {
    await this.saveDraftButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async publish() {
    await this.publishButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async preview() {
    await this.previewButton.click();
  }

  async cancel() {
    await this.cancelButton.click();
  }

  async goBack() {
    await this.backButton.click();
  }

  // Complete form filling
  async fillPostForm(data: {
    title: string;
    content: string;
    excerpt?: string;
    slug?: string;
    status?: "DRAFT" | "PUBLISHED" | "ARCHIVED";
    featured?: boolean;
    category?: string;
    tags?: string[];
    featuredImage?: string;
    metaTitle?: string;
    metaDescription?: string;
  }) {
    await this.fillTitle(data.title);
    
    if (data.slug) {
      await this.fillSlug(data.slug);
    }
    
    if (data.excerpt) {
      await this.fillExcerpt(data.excerpt);
    }
    
    await this.fillContent(data.content);
    
    if (data.status) {
      await this.selectStatus(data.status);
    }
    
    if (data.featured !== undefined) {
      await this.toggleFeatured(data.featured);
    }
    
    if (data.category) {
      await this.selectCategory(data.category);
    }
    
    if (data.tags && data.tags.length > 0) {
      await this.addTags(data.tags);
    }
    
    if (data.featuredImage) {
      await this.setFeaturedImage(data.featuredImage);
    }
    
    if (data.metaTitle) {
      await this.fillMetaTitle(data.metaTitle);
    }
    
    if (data.metaDescription) {
      await this.fillMetaDescription(data.metaDescription);
    }
  }

  // Validation and error checking
  async expectTitleError(message: string) {
    await expect(this.titleError).toContainText(message);
  }

  async expectContentError(message: string) {
    await expect(this.contentError).toContainText(message);
  }

  async expectSlugError(message: string) {
    await expect(this.slugError).toContainText(message);
  }

  async expectCategoryError(message: string) {
    await expect(this.categoryError).toContainText(message);
  }

  async expectNoErrors() {
    await expect(this.titleError).not.toBeVisible();
    await expect(this.contentError).not.toBeVisible();
    await expect(this.slugError).not.toBeVisible();
    await expect(this.categoryError).not.toBeVisible();
  }

  // State checks
  async expectSavingState() {
    await expect(this.savingIndicator).toBeVisible();
  }

  async expectLoadingState() {
    await expect(this.loadingSpinner).toBeVisible();
  }

  async expectFormDisabled() {
    await expect(this.titleInput).toBeDisabled();
    await expect(this.saveButton).toBeDisabled();
  }

  async expectFormEnabled() {
    await expect(this.titleInput).toBeEnabled();
    await expect(this.saveButton).toBeEnabled();
  }

  // Success/Error messages
  async expectSuccessMessage(message: string) {
    await expect(this.page.locator(`.toast:has-text("${message}")`)).toBeVisible();
  }

  async expectErrorMessage(message: string) {
    await expect(this.page.locator(`.toast:has-text("${message}")`)).toBeVisible();
  }

  // Navigation expectations
  async expectCreatePageTitle() {
    await expect(this.pageTitle).toContainText("Viết bài mới");
  }

  async expectEditPageTitle() {
    await expect(this.pageTitle).toContainText("Chỉnh sửa");
  }

  async expectRedirectToPostsList() {
    await expect(this.page).toHaveURL("/admin/posts");
  }

  // Get form values
  async getTitleValue() {
    return await this.titleInput.inputValue();
  }

  async getSlugValue() {
    return await this.slugInput.inputValue();
  }

  async getExcerptValue() {
    return await this.excerptTextarea.inputValue();
  }

  async getContentValue() {
    return await this.contentEditor.textContent();
  }

  async getSelectedStatus() {
    return await this.statusSelect.textContent();
  }

  async isFeatured() {
    return await this.featuredSwitch.isChecked();
  }

  async getSelectedTags() {
    return await this.page.locator('[data-testid="tag-item"]').allTextContents();
  }

  async hasFeaturedImage() {
    return await this.featuredImagePreview.isVisible();
  }

  // Editor interactions
  async formatTextBold() {
    await this.page.click('.ql-toolbar .ql-bold');
  }

  async formatTextItalic() {
    await this.page.click('.ql-toolbar .ql-italic');
  }

  async insertLink(url: string, text: string) {
    await this.page.click('.ql-toolbar .ql-link');
    await this.page.fill('[data-testid="link-url-input"]', url);
    await this.page.fill('[data-testid="link-text-input"]', text);
    await this.page.click('[data-testid="insert-link-btn"]');
  }

  async insertImage(imageUrl: string) {
    await this.page.click('.ql-toolbar .ql-image');
    await this.page.fill('[data-testid="image-url-input"]', imageUrl);
    await this.page.click('[data-testid="insert-image-btn"]');
  }
}
