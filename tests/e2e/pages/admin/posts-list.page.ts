import { Page, Locator, expect } from "@playwright/test";

export class PostsListPage {
  readonly page: Page;

  // Header elements
  readonly pageTitle: Locator;
  readonly pageDescription: Locator;
  readonly createPostButton: Locator;

  // Stats elements
  readonly totalPostsCount: Locator;
  readonly publishedPostsCount: Locator;
  readonly draftPostsCount: Locator;
  readonly featuredPostsCount: Locator;

  // Search and filters
  readonly searchInput: Locator;
  readonly statusFilter: Locator;
  readonly featuredFilter: Locator;
  readonly categoryFilter: Locator;
  readonly authorFilter: Locator;
  readonly clearFiltersButton: Locator;

  // Table elements
  readonly postsTable: Locator;
  readonly postRows: Locator;
  readonly selectAllCheckbox: Locator;
  readonly tableHeaders: Locator;

  // Bulk actions
  readonly bulkActionsBar: Locator;
  readonly selectedCountText: Locator;
  readonly bulkDeleteButton: Locator;
  readonly bulkPublishButton: Locator;
  readonly bulkArchiveButton: Locator;

  // Pagination
  readonly pagination: Locator;
  readonly pageInfo: Locator;
  readonly prevPageButton: Locator;
  readonly nextPageButton: Locator;
  readonly pageSizeSelect: Locator;

  // Loading and empty states
  readonly loadingSpinner: Locator;
  readonly emptyState: Locator;
  readonly errorMessage: Locator;
  readonly retryButton: Locator;

  // Dialogs
  readonly deleteDialog: Locator;
  readonly confirmDeleteButton: Locator;
  readonly cancelDeleteButton: Locator;
  readonly bulkDeleteDialog: Locator;
  readonly confirmBulkDeleteButton: Locator;

  constructor(page: Page) {
    this.page = page;

    // Header elements
    this.pageTitle = page.locator('h1:has-text("Quản lý bài viết")');
    this.pageDescription = page.locator(
      "text=Quản lý blog và nội dung marketing"
    );
    this.createPostButton = page.locator(
      '[data-testid="create-post-btn"], a[href="/admin/posts/create"]'
    );

    // Stats elements - Match PostsStats component structure
    this.totalPostsCount = page
      .locator('text="Tổng bài viết"')
      .locator("..")
      .locator(".text-2xl");
    this.publishedPostsCount = page
      .locator('text="Đã xuất bản"')
      .locator("..")
      .locator(".text-2xl");
    this.draftPostsCount = page
      .locator('text="Bản nháp"')
      .locator("..")
      .locator(".text-2xl");
    this.featuredPostsCount = page
      .locator('text="Nổi bật"')
      .locator("..")
      .locator(".text-2xl");

    // Search and filters - Match actual UI
    this.searchInput = page.locator(
      'input[placeholder="Tìm kiếm bài viết..."]'
    );
    this.statusFilter = page.locator('button:has-text("Lọc")');
    this.featuredFilter = page.locator('button:has-text("Lọc")');
    this.categoryFilter = page.locator('button:has-text("Lọc")');
    this.authorFilter = page.locator('button:has-text("Lọc")');
    this.clearFiltersButton = page.locator('button:has-text("Lọc")');

    // Table elements
    this.postsTable = page.locator("table");
    this.postRows = page.locator("tbody tr");
    this.selectAllCheckbox = page.locator('thead input[type="checkbox"]');
    this.tableHeaders = page.locator("thead th");

    // Bulk actions
    this.bulkActionsBar = page.locator('.bulk-actions, [class*="bulk"]');
    this.selectedCountText = page.locator(
      "text=/\\d+ selected/, text=/đã chọn/"
    );
    this.bulkDeleteButton = page.locator(
      'button:has-text("Xóa"), button:has-text("Delete")'
    );
    this.bulkPublishButton = page.locator(
      'button:has-text("Xuất bản"), button:has-text("Publish")'
    );
    this.bulkArchiveButton = page.locator(
      'button:has-text("Lưu trữ"), button:has-text("Archive")'
    );

    // Pagination
    this.pagination = page.locator('[data-testid="pagination"]');
    this.pageInfo = page.locator('[data-testid="page-info"]');
    this.prevPageButton = page.locator('[data-testid="prev-page-btn"]');
    this.nextPageButton = page.locator('[data-testid="next-page-btn"]');
    this.pageSizeSelect = page.locator('[data-testid="page-size-select"]');

    // Loading and empty states
    this.loadingSpinner = page.locator(
      '.loading, [class*="loading"], .spinner, [class*="spinner"]'
    );
    this.emptyState = page.locator(
      'td:has-text("Chưa có bài viết nào"), td:has-text("Không có dữ liệu")'
    );
    this.errorMessage = page.locator('.error, [class*="error"]');
    this.retryButton = page.locator(
      'button:has-text("Thử lại"), button:has-text("Retry")'
    );

    // Dialogs
    this.deleteDialog = page.locator('[data-testid="delete-dialog"]');
    this.confirmDeleteButton = page.locator(
      '[data-testid="confirm-delete-btn"]'
    );
    this.cancelDeleteButton = page.locator('[data-testid="cancel-delete-btn"]');
    this.bulkDeleteDialog = page.locator('[data-testid="bulk-delete-dialog"]');
    this.confirmBulkDeleteButton = page.locator(
      '[data-testid="confirm-bulk-delete-btn"]'
    );
  }

  async goto() {
    await this.page.goto("/admin/posts");
    await this.page.waitForLoadState("networkidle");
  }

  async waitForPageLoad() {
    await expect(this.pageTitle).toBeVisible();
    await this.page.waitForLoadState("networkidle");
  }

  // Search and filter actions
  async search(query: string) {
    await this.searchInput.fill(query);
    await this.searchInput.press("Enter");
    await this.page.waitForLoadState("networkidle");
  }

  async clearSearch() {
    await this.searchInput.clear();
    await this.searchInput.press("Enter");
    await this.page.waitForLoadState("networkidle");
  }

  async filterByStatus(status: string) {
    // Click filter button - actual UI only has filter button, not dropdowns
    await this.statusFilter.click();
    await this.page.waitForLoadState("networkidle");
  }

  async filterByFeatured(featured: boolean = true) {
    // Click filter button - actual UI only has filter button, not dropdowns
    await this.featuredFilter.click();
    await this.page.waitForLoadState("networkidle");
  }

  async filterByCategory(categoryName: string) {
    // Click filter button - actual UI only has filter button, not dropdowns
    await this.categoryFilter.click();
    await this.page.waitForLoadState("networkidle");
  }

  async clearAllFilters() {
    // Click filter button to clear
    await this.clearFiltersButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  // Table interactions
  async getPostRowByTitle(title: string) {
    return this.page.locator(`tbody tr:has-text("${title}")`);
  }

  async selectPostByIndex(index: number) {
    const row = this.postRows.nth(index);
    await row.locator('input[type="checkbox"]').check();
  }

  async selectPostByTitle(title: string) {
    const row = await this.getPostRowByTitle(title);
    await row.locator('input[type="checkbox"]').check();
  }

  async selectAllPosts() {
    await this.selectAllCheckbox.check();
  }

  async deselectAllPosts() {
    await this.selectAllCheckbox.uncheck();
  }

  // Post actions
  async editPost(title: string) {
    const row = await this.getPostRowByTitle(title);
    await row.locator('[data-testid="edit-btn"]').click();
  }

  async editPostByIndex(index: number) {
    const row = this.postRows.nth(index);
    await row.locator('[data-testid="edit-btn"]').click();
  }

  async deletePost(title: string) {
    const row = await this.getPostRowByTitle(title);
    await row.locator('[data-testid="delete-btn"]').click();
    await this.confirmDeleteButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async deletePostByIndex(index: number) {
    const row = this.postRows.nth(index);
    await row.locator("button").last().click(); // Use last button in row as action button
    await this.confirmDeleteButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async toggleFeatured(title: string) {
    const row = await this.getPostRowByTitle(title);
    await row.locator("button").last().click(); // Use last button in row as action button
    await this.page.waitForLoadState("networkidle");
  }

  async viewPost(title: string) {
    const row = await this.getPostRowByTitle(title);
    await row.locator("button").last().click(); // Use last button in row as action button
  }

  // Bulk actions
  async bulkDelete() {
    await this.bulkDeleteButton.click();
    await this.confirmBulkDeleteButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async bulkPublish() {
    await this.bulkPublishButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async bulkArchive() {
    await this.bulkArchiveButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  // Sorting
  async sortByTitle(order: "asc" | "desc" = "asc") {
    const titleHeader = this.page.locator(
      'th:has-text("Tiêu đề"), th:has-text("Title")'
    );
    await titleHeader.click();
    if (order === "desc") {
      await titleHeader.click(); // Click again for descending
    }
    await this.page.waitForLoadState("networkidle");
  }

  async sortByDate(order: "asc" | "desc" = "desc") {
    const dateHeader = this.page.locator('th:has-text("Ngày tạo")');
    await dateHeader.click();
    if (order === "asc") {
      await dateHeader.click(); // Click again for ascending
    }
    await this.page.waitForLoadState("networkidle");
  }

  async sortByStatus(order: "asc" | "desc" = "asc") {
    const statusHeader = this.page.locator('th:has-text("Trạng thái")');
    await statusHeader.click();
    if (order === "desc") {
      await statusHeader.click(); // Click again for descending
    }
    await this.page.waitForLoadState("networkidle");
  }

  // Pagination
  async goToNextPage() {
    await this.nextPageButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async goToPrevPage() {
    await this.prevPageButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async goToPage(pageNumber: number) {
    await this.page.click(`[data-testid="page-${pageNumber}"]`);
    await this.page.waitForLoadState("networkidle");
  }

  async changePageSize(size: number) {
    await this.pageSizeSelect.click();
    await this.page.click(`text=${size}`);
    await this.page.waitForLoadState("networkidle");
  }

  // Navigation
  async clickCreatePost() {
    await this.createPostButton.click();
  }

  // Assertions
  async expectPostsCount(count: number) {
    await expect(this.postRows).toHaveCount(count);
  }

  async expectPostVisible(title: string) {
    await expect(this.page.locator(`text=${title}`)).toBeVisible();
  }

  async expectPostNotVisible(title: string) {
    await expect(this.page.locator(`text=${title}`)).not.toBeVisible();
  }

  async expectEmptyState() {
    await expect(this.emptyState).toBeVisible();
  }

  async expectLoadingState() {
    await expect(this.loadingSpinner).toBeVisible();
  }

  async expectErrorState() {
    await expect(this.errorMessage).toBeVisible();
  }

  async expectBulkActionsVisible() {
    await expect(this.bulkActionsBar).toBeVisible();
  }

  async expectBulkActionsHidden() {
    await expect(this.bulkActionsBar).not.toBeVisible();
  }

  async expectSelectedCount(count: number) {
    await expect(this.selectedCountText).toContainText(`${count} đã chọn`);
  }

  async expectPageInfo(current: number, total: number) {
    await expect(this.pageInfo).toContainText(`${current} / ${total}`);
  }

  async expectSuccessToast(message: string) {
    await expect(
      this.page.locator(`.toast:has-text("${message}")`)
    ).toBeVisible();
  }

  async expectErrorToast(message: string) {
    await expect(
      this.page.locator(`.toast:has-text("${message}")`)
    ).toBeVisible();
  }

  // Get data methods
  async getPostTitles() {
    return await this.page
      .locator('[data-testid="post-title"]')
      .allTextContents();
  }

  async getPostStatuses() {
    return await this.page
      .locator('[data-testid="post-status"]')
      .allTextContents();
  }

  async getSelectedPostsCount() {
    const selectedCheckboxes = await this.page
      .locator(
        '[data-testid="post-row"] [data-testid="select-checkbox"]:checked'
      )
      .count();
    return selectedCheckboxes;
  }

  async getCurrentPageNumber() {
    const pageInfo = await this.pageInfo.textContent();
    if (pageInfo) {
      const match = pageInfo.match(/(\d+) \/ \d+/);
      return match ? parseInt(match[1]) : 1;
    }
    return 1;
  }

  async getTotalPages() {
    const pageInfo = await this.pageInfo.textContent();
    if (pageInfo) {
      const match = pageInfo.match(/\d+ \/ (\d+)/);
      return match ? parseInt(match[1]) : 1;
    }
    return 1;
  }
}
