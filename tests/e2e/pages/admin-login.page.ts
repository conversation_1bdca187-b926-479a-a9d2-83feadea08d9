import { Page, Locator, expect } from "@playwright/test";

export class AdminLoginPage {
  readonly page: Page;

  // Form elements
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly loginButton: Locator;
  readonly rememberMeCheckbox: Locator;

  // Error and success messages
  readonly errorMessage: Locator;
  readonly successMessage: Locator;

  // Page elements
  readonly pageTitle: Locator;
  readonly loginForm: Locator;
  readonly forgotPasswordLink: Locator;

  // Loading states
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;

    // Form elements
    this.emailInput = page.locator('input[type="email"], input[name="email"]');
    this.passwordInput = page.locator(
      'input[type="password"], input[name="password"]'
    );
    this.loginButton = page.locator('button[type="submit"]');
    this.rememberMeCheckbox = page.locator(
      'input[type="checkbox"][name="remember"]'
    );

    // Error and success messages
    this.errorMessage = page.locator(
      '[data-testid="error-message"], .error-message, .alert-error'
    );
    this.successMessage = page.locator(
      '[data-testid="success-message"], .success-message, .alert-success'
    );

    // Page elements
    this.pageTitle = page.locator("h1, h2").first();
    this.loginForm = page.locator('form, [data-testid="login-form"]');
    this.forgotPasswordLink = page.locator('a:has-text("Quên mật khẩu")');

    // Loading states
    this.loadingSpinner = page.locator(
      '[data-testid="loading-spinner"], .loading-spinner'
    );
  }

  async goto() {
    await this.page.goto("/admin/auth/signin");
    // Use domcontentloaded instead of networkidle to avoid timeouts
    await this.page.waitForLoadState("domcontentloaded");
  }

  async waitForPageLoad() {
    await expect(this.loginForm).toBeVisible();
    await this.page.waitForLoadState("networkidle");
  }

  async login(email: string, password: string, rememberMe: boolean = false) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);

    if (rememberMe) {
      await this.rememberMeCheckbox.check();
    }

    await this.loginButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async expectLoginSuccess() {
    // Should redirect to admin dashboard (not auth pages)
    await expect(this.page).toHaveURL(/\/admin(?!\/auth)/);
  }

  async expectLoginError(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  async expectFormDisabled() {
    await expect(this.emailInput).toBeDisabled();
    await expect(this.passwordInput).toBeDisabled();
    await expect(this.loginButton).toBeDisabled();
  }

  async expectFormEnabled() {
    await expect(this.emailInput).toBeEnabled();
    await expect(this.passwordInput).toBeEnabled();
    await expect(this.loginButton).toBeEnabled();
  }

  async expectLoadingState() {
    await expect(this.loadingSpinner).toBeVisible();
  }

  async clickForgotPassword() {
    await this.forgotPasswordLink.click();
  }

  async getEmailValue() {
    return await this.emailInput.inputValue();
  }

  async getPasswordValue() {
    return await this.passwordInput.inputValue();
  }

  async isRememberMeChecked() {
    return await this.rememberMeCheckbox.isChecked();
  }
}
