#!/bin/bash

# Posts Management Testing Script
# Usage: ./scripts/test-posts.sh [option]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if development server is running
check_dev_server() {
    print_status "Checking if development server is running..."
    if curl -s http://localhost:3000 > /dev/null; then
        print_success "Development server is running"
        return 0
    else
        print_warning "Development server is not running"
        return 1
    fi
}

# Function to start development server
start_dev_server() {
    print_status "Starting development server..."
    npm run dev &
    DEV_SERVER_PID=$!
    
    # Wait for server to start
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null; then
            print_success "Development server started successfully"
            return 0
        fi
        sleep 2
    done
    
    print_error "Failed to start development server"
    return 1
}

# Function to stop development server
stop_dev_server() {
    if [ ! -z "$DEV_SERVER_PID" ]; then
        print_status "Stopping development server..."
        kill $DEV_SERVER_PID 2>/dev/null || true
        print_success "Development server stopped"
    fi
}

# Function to run all posts tests
run_all_tests() {
    print_status "Running all posts management tests..."
    npx playwright test tests/e2e/admin/posts/ --reporter=html
    print_success "All tests completed"
}

# Function to run specific test category
run_list_tests() {
    print_status "Running posts list tests..."
    npx playwright test tests/e2e/admin/posts/posts-list.spec.ts --reporter=html
}

run_create_tests() {
    print_status "Running create post tests..."
    npx playwright test tests/e2e/admin/posts/create-post.spec.ts --reporter=html
}

run_edit_tests() {
    print_status "Running edit post tests..."
    npx playwright test tests/e2e/admin/posts/edit-post.spec.ts --reporter=html
}

run_detail_tests() {
    print_status "Running post detail/preview tests..."
    npx playwright test tests/e2e/admin/posts/post-detail.spec.ts --reporter=html
}

run_stats_tests() {
    print_status "Running posts stats tests..."
    npx playwright test tests/e2e/admin/posts/posts-stats.spec.ts --reporter=html
}

run_api_tests() {
    print_status "Running posts API tests..."
    npx playwright test tests/e2e/admin/posts/posts-api.spec.ts --reporter=html
}

run_permission_tests() {
    print_status "Running posts permission tests..."
    npx playwright test tests/e2e/admin/posts/posts-permissions.spec.ts --reporter=html
}

# Function to run tests with UI mode
run_ui_tests() {
    print_status "Running tests in UI mode..."
    npx playwright test tests/e2e/admin/posts/ --ui
}

# Function to run tests in debug mode
run_debug_tests() {
    print_status "Running tests in debug mode..."
    npx playwright test tests/e2e/admin/posts/ --debug
}

# Function to run tests with specific browser
run_browser_tests() {
    local browser=$1
    print_status "Running tests on $browser browser..."
    npx playwright test tests/e2e/admin/posts/ --project=$browser --reporter=html
}

# Function to show test report
show_report() {
    print_status "Opening test report..."
    npx playwright show-report
}

# Function to install dependencies
install_deps() {
    print_status "Installing dependencies..."
    npm install
    npx playwright install
    print_success "Dependencies installed"
}

# Function to clean test data
clean_test_data() {
    print_status "Cleaning test data..."
    # This would run a cleanup script if available
    # node scripts/cleanup-test-data.js
    print_success "Test data cleaned"
}

# Function to show help
show_help() {
    echo "Posts Management Testing Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  all              Run all posts tests"
    echo "  list             Run posts list tests"
    echo "  create           Run create post tests"
    echo "  edit             Run edit post tests"
    echo "  detail           Run post detail/preview tests"
    echo "  stats            Run posts stats tests"
    echo "  api              Run posts API tests"
    echo "  permissions      Run posts permission tests"
    echo "  ui               Run tests in UI mode"
    echo "  debug            Run tests in debug mode"
    echo "  chromium         Run tests on Chromium"
    echo "  firefox          Run tests on Firefox"
    echo "  webkit           Run tests on WebKit"
    echo "  report           Show test report"
    echo "  install          Install dependencies"
    echo "  clean            Clean test data"
    echo "  help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 all           # Run all tests"
    echo "  $0 list          # Run only list tests"
    echo "  $0 ui            # Run tests with UI"
    echo "  $0 chromium      # Run tests on Chromium only"
}

# Trap to ensure cleanup on exit
trap 'stop_dev_server' EXIT

# Main script logic
main() {
    local option=${1:-"help"}
    
    case $option in
        "all")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_all_tests
            ;;
        "list")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_list_tests
            ;;
        "create")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_create_tests
            ;;
        "edit")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_edit_tests
            ;;
        "detail")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_detail_tests
            ;;
        "stats")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_stats_tests
            ;;
        "api")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_api_tests
            ;;
        "permissions")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_permission_tests
            ;;
        "ui")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_ui_tests
            ;;
        "debug")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_debug_tests
            ;;
        "chromium"|"firefox"|"webkit")
            if ! check_dev_server; then
                start_dev_server
            fi
            run_browser_tests $option
            ;;
        "report")
            show_report
            ;;
        "install")
            install_deps
            ;;
        "clean")
            clean_test_data
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
